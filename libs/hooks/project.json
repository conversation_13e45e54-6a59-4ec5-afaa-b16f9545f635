{"name": "hooks", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/hooks/src", "projectType": "library", "tags": ["frontend"], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/hooks/**/*.{ts,tsx,js,jsx}"]}}, "build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/libs/hooks"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/libs/hooks"}}}}