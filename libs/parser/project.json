{"name": "parser", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/parser/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/parser", "main": "libs/parser/src/index.ts", "tsConfig": "libs/parser/tsconfig.lib.json", "assets": ["libs/parser/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/parser/**/*.ts", "libs/parser/package.json"]}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/libs/parser"}}}, "tags": ["frontend"]}