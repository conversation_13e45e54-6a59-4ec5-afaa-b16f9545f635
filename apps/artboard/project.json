{"name": "artboard", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/artboard/src", "projectType": "application", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/artboard"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "artboard:build"}, "configurations": {"development": {"buildTarget": "artboard:build:development", "hmr": true}, "production": {"buildTarget": "artboard:build:production", "hmr": false}}}, "preview": {"executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "artboard:build"}, "configurations": {"development": {"buildTarget": "artboard:build:development"}, "production": {"buildTarget": "artboard:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/artboard/**/*.{ts,tsx,js,jsx}"]}}}, "tags": ["frontend"]}