{"name": "server", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/server/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "dependsOn": ["^build"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/server", "main": "apps/server/src/main.ts", "tsConfig": "apps/server/tsconfig.app.json", "assets": ["apps/server/src/assets"], "webpackConfig": "apps/server/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "server:build"}, "configurations": {"development": {"buildTarget": "server:build:development"}, "production": {"buildTarget": "server:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/server/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/server/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": ["backend"]}