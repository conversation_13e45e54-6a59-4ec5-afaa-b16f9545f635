msgid ""
msgstr ""
"POT-Creation-Date: 2023-11-10 13:15+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: bg\n"
"Project-Id-Version: reactive-resume\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-02-03 09:13\n"
"Last-Translator: \n"
"Language-Team: Bulgarian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: reactive-resume\n"
"X-Crowdin-Project-ID: 503410\n"
"X-Crowdin-Language: bg\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 494\n"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:171
msgid "You have enabled two-factor authentication successfully."
msgstr "Успешно сте активирали двуфакторното удостоверяване."

#: apps/client/src/pages/home/<USER>/features/index.tsx:57
msgid "{templatesCount} resume templates to choose from"
msgstr "{templatesCount} шаблони на автобиографии, от които да избирате"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:142
msgid "{value, plural, one {Column} other {Columns}}"
msgstr "{value, plural, one {Колона} other {Колони}}"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:20
msgid "<0>I built Reactive Resume mostly by myself during my spare time, with a lot of help from other great open-source contributors.</0><1>If you like the app and want to support keeping it free forever, please donate whatever you can afford to give.</1>"
msgstr "<0>Създадох Reactive Resume предимно сам през свободното си време, с голяма помощ от други големи автори на отворен код.</0><1>Ако харесвате приложението и искате да подкрепите запазването на безплатността му завинаги, моля, дарете каквото можете да си позволите.</1>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:51
msgid "<0>I'm sure the app is not perfect, but I'd like for it to be.</0><1>If you faced any issues while creating your resume, or have an idea that would help you and other users in creating your resume more easily, drop an issue on the repository or send me an email about it.</1>"
msgstr "<0>Сигурен съм, че приложението не е съвършено, но бих искал да бъде.</0><1>Ако сте се сблъскали с някакви проблеми при създаването на автобиографията си или имате идея, която би помогнала на вас и на други потребители да създадете по-лесно автобиографията си, напишете въпрос в хранилището или ми изпратете имейл за това.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:201
msgid "<0>Note: </0>By utilizing the OpenAI API, you acknowledge and accept the <1>terms of use</1> and <2>privacy policy</2> outlined by OpenAI. Please note that Reactive Resume bears no responsibility for any improper or unauthorized utilization of the service, and any resulting repercussions or liabilities solely rest on the user."
msgstr "<0>Забележка: </0>Използвайки API на OpenAI, вие потвърждавате и приемате <1>условията за използване</1> и <2>политиката за поверителност,</2> описани от OpenAI. Моля, обърнете внимание, че Reactive Resume не носи отговорност за неправилно или неразрешено използване на услугата и всички произтичащи от това последствия или отговорности са единствено за сметка на потребителя."

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:85
msgid "<0>The community has spent a lot of time writing the documentation for Reactive Resume, and I'm sure it will help you get started with the app.</0><1>There are also a lot of examples to help you get started, and features that you might not know about which could help you build your perfect resume.</1>"
msgstr "<0>Общността е отделила много време, за да напише документацията за Reactive Resume, и съм сигурен, че тя ще ви помогне да започнете работа с приложението.</0><1>Има и много примери, които ще ви помогнат да започнете, както и функции, за които може би не знаете и които биха ви помогнали да създадете перфектното си CV.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:140
msgid "<0>Two-factor authentication is currently disabled.</0> You can enable it by adding an authenticator app to your account."
msgstr "<0>Понастоящем двуфакторното удостоверяване е деактивирано.</0> Можете да я активирате, като добавите приложение за удостоверяване към профила си."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:133
msgid "<0>Two-factor authentication is enabled.</0> You will be asked to enter a code every time you sign in."
msgstr "<0>Разрешено е двуфакторно удостоверяване.</0> Всеки път, когато влизате в системата, ще бъдете помолени да въведете код."

#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/home/<USER>/hero/index.tsx:40
msgid "A free and open-source resume builder"
msgstr "Безплатен конструктор на автобиографии с отворен код"

#: apps/client/src/pages/home/<USER>/footer.tsx:21
#: apps/client/src/pages/home/<USER>/hero/index.tsx:45
msgid "A free and open-source resume builder that simplifies the process of creating, updating, and sharing your resume."
msgstr "Безплатен конструктор на автобиографии с отворен код, който опростява процеса на създаване, актуализиране и споделяне на автобиографията ви."

#: apps/client/src/pages/builder/_components/toolbar.tsx:59
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:29
msgid "A link has been copied to your clipboard."
msgstr "Връзката е копирана във вашия клипборд."

#: apps/client/src/components/copyright.tsx:29
msgid "A passion project by <0>Amruth Pillai</0>"
msgstr "Страстен проект на <0>Amruth Pillai</0>"

#: apps/client/src/pages/auth/forgot-password/page.tsx:57
msgid "A password reset link should have been sent to your inbox, if an account existed with the email you provided."
msgstr "В пощенската ви кутия трябва да бъде изпратена връзка за нулиране на паролата, ако съществува акаунт с предоставения от вас имейл."

#: apps/client/src/services/errors/translate-error.ts:43
msgid "A resume with this slug already exists, please pick a different unique identifier."
msgstr "Вече съществува автобиография с този слог, моля, изберете друг уникален идентификатор."

#: apps/client/src/services/errors/translate-error.ts:10
msgid "A user with this email address and/or username already exists."
msgstr "Потребител с този имейл адрес и/или потребителско име вече съществува."

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:43
msgid "A4"
msgstr "A4"

#. Helper text to let the user know what filetypes are accepted. {accept} can be .pdf or .json.
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:270
msgid "Accepts only {accept} files"
msgstr "Приема само файлове {accept}"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:105
msgid "Account"
msgstr "Сметка"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:175
msgid "Add a custom field"
msgstr "Добавяне на потребителско поле"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:119
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:170
msgctxt "For example, add a new work experience, or add a new profile."
msgid "Add a new item"
msgstr "Добавяне на нов елемент"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:91
msgid "Add a new item"
msgstr "Добавяне на нов елемент"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:146
#: apps/client/src/pages/builder/sidebars/left/index.tsx:263
msgid "Add a new section"
msgstr "Добавяне на нов раздел"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:261
msgid "Add New Page"
msgstr "Добавяне на нова страница"

#: apps/client/src/components/ai-actions.tsx:79
msgid "AI"
msgstr "AI"

#: apps/client/src/pages/auth/register/page.tsx:71
msgid "Already have an account?"
msgstr "Вече имате акаунт?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:144
msgid "An error occurred while validating the file."
msgstr "Възникна грешка при валидирането на файла."

#: apps/client/src/pages/public/error.tsx:23
msgid "An internal server error occurred."
msgstr ""

#: apps/client/src/pages/public/error.tsx:32
msgid "An unexpected error occurred."
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:134
msgid "and many more..."
msgstr "и много други..."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:57
msgid "Anyone with the link can view and download the resume."
msgstr "Всеки, който има връзка, може да прегледа и изтегли автобиографията."

#: apps/client/src/pages/builder/_components/toolbar.tsx:60
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:30
msgid "Anyone with this link can view and download the resume. Share it on your profile or with recruiters."
msgstr "Всеки, който използва тази връзка, може да прегледа и изтегли автобиографията. Споделете я в профила си или с лица, набиращи персонал."

#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:41
msgid "Apply Custom CSS"
msgstr "Прилагане на потребителски CSS"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:128
msgid "Are you sure you want to delete this item?"
msgstr "Сигурни ли сте, че искате да изтриете този елемент?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:149
msgid "Are you sure you want to delete your resume?"
msgstr "Сигурни ли сте, че искате да изтриете автобиографията си?"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:125
msgid "Are you sure you want to disable two-factor authentication?"
msgstr "Сигурни ли сте, че искате да деактивирате двуфакторното удостоверяване?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:38
msgid "Are you sure you want to lock this resume?"
msgstr "Сигурни ли сте, че искате да заключите това резюме?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:39
msgid "Are you sure you want to unlock this resume?"
msgstr "Сигурни ли сте, че искате да отключите това резюме?"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Are you sure?"
msgstr "Сигурни ли сте?"

#. For example, Computer Science or Business Administration
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:73
msgid "Area of Study"
msgstr "Област на изследване"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:79
msgid "Aspect Ratio"
msgstr "Съотношение на страните"

#: apps/client/src/pages/home/<USER>/features/index.tsx:51
msgid "Available in {languagesCount} languages"
msgstr "Предлага се на {languagesCount} езици"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:53
msgid "Awarder"
msgstr "Награждаващ"

#: apps/client/src/pages/auth/backup-otp/page.tsx:99
#: apps/client/src/pages/auth/forgot-password/page.tsx:100
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:243
msgid "Back"
msgstr "Обратно"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:73
msgid "Background Color"
msgstr "Цвят на фона"

#: apps/client/src/pages/auth/backup-otp/page.tsx:75
msgid "Backup Code"
msgstr "Код за резервно копие"

#: apps/client/src/pages/auth/backup-otp/page.tsx:81
msgid "Backup Codes may contain only lowercase letters or numbers, and must be exactly 10 characters."
msgstr "Кодовете за архивиране могат да съдържат само малки букви или цифри и трябва да са точно 10 символа."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:132
msgid "Base URL"
msgstr "Базов URL адрес"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:55
msgctxt "The basics section of a resume consists of User's Picture, Full Name, Location etc."
msgid "Basics"
msgstr "Основи"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:21
msgid "Basics"
msgstr "Основи"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:191
msgid "Border"
msgstr "Граница"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:124
msgid "Border Radius"
msgstr "Радиус на границата"

#: apps/client/src/pages/public/page.tsx:93
msgid "Built with"
msgstr "Изграден с"

#: apps/client/src/components/copyright.tsx:27
#: apps/client/src/pages/home/<USER>/contributors/index.tsx:20
msgid "By the community, for the community."
msgstr "От общността, за общността."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:135
#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:49
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:156
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:137
msgid "Cancel"
msgstr "Отмяна на"

#: apps/client/src/components/ai-actions.tsx:103
#: apps/client/src/components/ai-actions.tsx:106
msgid "Casual"
msgstr "Случайно"

#: apps/client/src/pages/builder/_components/toolbar.tsx:130
msgid "Center Artboard"
msgstr "Център Артборд"

#: apps/client/src/pages/auth/reset-password/page.tsx:99
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:115
msgid "Change Password"
msgstr "Промяна на паролата"

#: apps/client/src/components/ai-actions.tsx:97
msgid "Change Tone"
msgstr "Промяна на тона"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:186
msgid "Changed your mind about the name? Give it a new one."
msgstr "Променихте мнението си за името? Дайте му ново."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:70
msgid "Check your email for the confirmation link to update your email address."
msgstr "Проверете имейла си за връзка за потвърждение, за да актуализирате имейл адреса си."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:144
msgid "Circle"
msgstr "Кръг"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:249
msgid "Close"
msgstr "Затвори"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:201
msgid "Code"
msgstr "Код"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:52
msgid "Code must be exactly 6 digits long."
msgstr "Кодът трябва да е дълъг точно 6 цифри."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:136
msgid "Columns"
msgstr "Колони"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:39
msgid "Company"
msgstr "Компания"

#: apps/client/src/components/ai-actions.tsx:115
#: apps/client/src/components/ai-actions.tsx:118
msgid "Confident"
msgstr "Уверен"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:234
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:246
msgid "Continue"
msgstr "Продължи"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:96
msgid "Copy"
msgstr "Копие"

#: apps/client/src/pages/builder/_components/toolbar.tsx:164
msgid "Copy Link to Resume"
msgstr "Копиране на връзката към автобиографията"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:78
msgid "Copy to Clipboard"
msgstr "Копиране в клипборда"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:179
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:244
msgid "Create"
msgstr "Създаване на"

#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/register/page.tsx:69
msgid "Create a new account"
msgstr "Създаване на нов акаунт"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:163
msgid "Create a new item"
msgstr "Създаване на нов елемент"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:178
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:18
msgid "Create a new resume"
msgstr "Създаване на нова автобиография"

#: apps/client/src/pages/auth/login/page.tsx:65
msgctxt "This is a link to create a new account"
msgid "Create one now"
msgstr "Създайте такъв сега"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:259
msgid "Create Sample Resume"
msgstr "Създаване на примерна автобиография"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:82
msgid "Current Password"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/index.tsx:93
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:27
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:28
msgid "Custom CSS"
msgstr "Потребителски CSS"

#: apps/client/src/pages/home/<USER>/features/index.tsx:62
msgid "Custom resume sections"
msgstr "Персонализирани раздели на автобиографията"

#: apps/client/src/stores/resume.ts:47
msgid "Custom Section"
msgstr "Потребителски раздел"

#: apps/client/src/pages/home/<USER>/features/index.tsx:60
msgid "Customisable colour palettes"
msgstr "Цветови палитри с възможност за персонализиране"

#: apps/client/src/pages/home/<USER>/features/index.tsx:61
msgid "Customisable layouts"
msgstr "Възможност за персонализиране на оформлението"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:62
msgid "Danger Zone"
msgstr "Опасна зона"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:87
msgid "Dark"
msgstr "Dark"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:67
msgid "Date"
msgstr "Дата"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:110
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:72
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:67
msgid "Date or Date Range"
msgstr "Дата или диапазон от дати"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:137
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:158
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:121
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:127
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:172
msgid "Delete"
msgstr "Изтриване на"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:79
#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Delete Account"
msgstr "Изтриване на профила"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:50
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:53
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:63
msgid "Description"
msgstr "Описание"

#: apps/client/src/pages/home/<USER>/features/index.tsx:58
msgid "Design single/multi page resumes"
msgstr "Проектиране на автобиографии от една/няколко страници"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:139
msgid "Disable"
msgstr "Деактивиране на"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:154
msgid "Disable 2FA"
msgstr "Деактивиране на 2FA"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:302
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:220
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:134
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:118
msgid "Discard"
msgstr "Изхвърляне"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:105
msgid "Documentation"
msgstr "Документация"

#: apps/client/src/pages/auth/login/page.tsx:62
msgid "Don't have an account?"
msgstr "Нямате акаунт?"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:83
msgid "Don't know where to begin? Hit the docs!"
msgstr "Не знаете откъде да започнете? Обърнете се към документите!"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:107
msgid "Don't see your language? <0>Help translate the app.</0>"
msgstr "Не виждате своя език? <0>Помогнете за превода на приложението.</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:40
msgid "Donate to Reactive Resume"
msgstr "Дарете за Reactive Resume"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:56
msgid "Download a JSON snapshot of your resume. This file can be used to import your resume in the future, or can even be shared with others to collaborate."
msgstr "Изтеглете JSON снимка на автобиографията си. Този файл може да се използва за импортиране на автобиографията ви в бъдеще или дори да бъде споделен с други хора за съвместна работа."

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:74
msgid "Download a PDF of your resume. This file can be used to print your resume, send it to recruiters, or upload on job portals."
msgstr "Изтеглете PDF файл с автобиографията си. Този файл може да се използва за отпечатване на автобиографията ви, за изпращане до специалисти по подбор на персонал или за качване в портали за работа."

#: apps/client/src/pages/builder/_components/toolbar.tsx:176
msgid "Download PDF"
msgstr "Изтегляне на PDF"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:58
msgid "Downloads"
msgstr "Изтегляне"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:181
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:246
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:95
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:156
msgid "Duplicate"
msgstr "Дублиране на"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:165
msgid "Duplicate an existing item"
msgstr "Дублиране на съществуващ елемент"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:180
msgid "Duplicate an existing resume"
msgstr "Дублиране на съществуваща автобиография"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:92
msgid "Edit"
msgstr "Редактиране на"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:169
msgid "Effects"
msgstr "Ефекти"

#: apps/client/src/pages/auth/forgot-password/page.tsx:82
#: apps/client/src/pages/auth/login/page.tsx:90
#: apps/client/src/pages/auth/register/page.tsx:141
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:54
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:181
msgid "Email"
msgstr "Имейл"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:163
msgid "Enable 2FA"
msgstr "Активиране на 2FA"

#: apps/client/src/pages/auth/reset-password/page.tsx:67
msgid "Enter a new password below, and make sure it's secure."
msgstr "Въведете нова парола по-долу и се уверете, че тя е сигурна."

#: apps/client/src/pages/auth/backup-otp/page.tsx:59
msgid "Enter one of the 10 backup codes you saved when you enabled two-factor authentication."
msgstr "Въведете един от 10-те кода за резервно копие, които сте запазили, когато сте активирали двуфакторното удостоверяване."

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:63
msgid "Enter Phosphor Icon"
msgstr "Въведете икона на фосфора"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:170
msgid "Enter the 6-digit code from your authenticator app to verify that 2FA has been setup correctly."
msgstr "Въведете 6-цифрения код от приложението за удостоверяване на автентичността, за да проверите дали 2FA е настроено правилно."

#: apps/client/src/pages/auth/verify-otp/page.tsx:60
msgid "Enter the one-time password provided by your authenticator app below."
msgstr "Въведете еднократната парола, предоставена от вашето приложение за удостоверяване на автентичността, по-долу."

#: apps/client/src/pages/auth/forgot-password/page.tsx:70
msgid "Enter your email address and we will send you a link to reset your password if the account exists."
msgstr "Въведете имейл адреса си и ние ще ви изпратим връзка за възстановяване на паролата, ако акаунтът съществува."

#: apps/client/src/pages/public/error.tsx:46
msgid "Error {statusCode}"
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:283
msgid "Errors"
msgstr "Грешки"

#: apps/client/src/pages/home/<USER>/support/index.tsx:78
msgid "Even if you're not in a position to contribute financially, you can still make a difference by giving the GitHub repository a star, spreading the word to your friends, or dropping a quick message to let me know how Reactive Resume has helped you. Your feedback and support are always welcome and much appreciated!"
msgstr "Дори и да не сте в състояние да дадете финансов принос, все пак можете да допринесете за промяната, като дадете на хранилището в GitHub звезда, разпространите информацията сред приятелите си или ми изпратите кратко съобщение, за да ми кажете как ви е помогнало Reactive Resume. Вашата обратна връзка и подкрепа са винаги добре дошли и много ценени!"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:12
msgid "Explore the templates available in Reactive Resume and view the resumes crafted with them. They could also serve as examples to help guide the creation of your next resume."
msgstr "Разгледайте шаблоните, налични в Reactive Resume, и вижте автобиографиите, създадени с тях. Те могат да послужат и като примери, които да ви помогнат да създадете следващата си автобиография."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:121
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:39
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:40
msgid "Export"
msgstr "Експорт"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:255
msgid "File"
msgstr "Файл"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:221
msgid "Filetype"
msgstr "Тип на файла"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:38
msgid "Finally,"
msgstr "Накрая,"

#: apps/client/src/components/ai-actions.tsx:90
msgid "Fix Spelling & Grammar"
msgstr "Поправяне на правописа и граматиката"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:107
msgid "Font Family"
msgstr "Семейство шрифтове"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:148
msgid "Font Size"
msgstr "Размер на шрифта"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:122
msgid "Font Subset"
msgstr "Подгрупа шрифтове"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:134
msgid "Font Variants"
msgstr "Варианти на шрифта"

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:35
msgid "For example, information regarding which companies you sent this resume to or the links to the job descriptions can be noted down here."
msgstr "Например тук може да се запише информация за компаниите, на които сте изпратили автобиографията, или връзки към описанията на длъжностите."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:182
msgid "Forget"
msgstr "Забравете"

#: apps/client/src/pages/auth/login/page.tsx:131
msgid "Forgot Password?"
msgstr "Забравена парола?"

#: apps/client/src/pages/auth/forgot-password/page.tsx:68
msgid "Forgot your password?"
msgstr "Забравили сте паролата си?"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:32
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:40
msgid "Format"
msgstr "Формат"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:49
msgid "Found a bug, or have an idea for a new feature?"
msgstr "Открили сте грешка или имате идея за нова функция?"

#: apps/client/src/pages/home/<USER>/features/index.tsx:46
msgid "Free, forever"
msgstr "Безплатно, завинаги"

#: apps/client/src/components/ai-actions.tsx:121
#: apps/client/src/components/ai-actions.tsx:124
msgid "Friendly"
msgstr "Приятелски"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:31
msgid "Full Name"
msgstr "Пълно име"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:202
msgid "Generate a random title for your resume"
msgstr "Генериране на произволно заглавие за автобиографията ви"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:32
msgid "Get Started"
msgstr "Започнете"

#: apps/client/src/pages/auth/_components/social-auth.tsx:18
msgid "GitHub"
msgstr "GitHub"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:12
msgid "GitHub Stars"
msgstr "Звезди на GitHub"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:187
msgid "Give your old resume a new name."
msgstr "Дайте ново име на старото си резюме."

#: apps/client/src/pages/auth/verify-email/page.tsx:67
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:18
msgid "Go to Dashboard"
msgstr "Отидете на таблото за управление"

#: apps/client/src/pages/public/error.tsx:55
msgid "Go to home"
msgstr ""

#: apps/client/src/pages/auth/_components/social-auth.tsx:31
msgid "Google"
msgstr "Google"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:202
msgid "Grayscale"
msgstr "Сива скала"

#: apps/client/src/pages/dashboard/resumes/page.tsx:43
msgid "Grid"
msgstr "Мрежа"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:43
msgid "Headline"
msgstr "Заглавие"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:107
msgid "Here, you can update your account information such as your profile picture, name and username."
msgstr "Тук можете да актуализирате информацията в профила си, например профилната си снимка, името и потребителското си име."

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:68
msgid "Here, you can update your profile to customize and personalize your experience."
msgstr "Тук можете да актуализирате профила си, за да персонализирате работата си."

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:80
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:94
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:180
msgid "Hidden"
msgstr "Скрит"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Hide"
msgstr "Скрий"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:192
msgid "Hide Icons"
msgstr "Скриване на икони"

#: apps/client/src/pages/auth/login/page.tsx:115
#: apps/client/src/pages/auth/register/page.tsx:168
#: apps/client/src/pages/auth/reset-password/page.tsx:88
msgid "Hold <0>Ctrl</0> to display your password temporarily."
msgstr "Задръжте <0>Ctrl</0>, за да покажете временно паролата си."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:93
msgid "Horizontal"
msgstr "Хоризонтален"

#: apps/client/src/pages/home/<USER>/features/index.tsx:67
msgid "Host your resume publicly"
msgstr "Публично хостване на автобиографията ви"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:70
msgid "I always love to hear from the users of Reactive Resume with feedback or support. Here are some of the messages I've received. If you have any feedback, feel free to drop me an email at <0>{email}</0>."
msgstr "Винаги се радвам да чуя от потребителите на Reactive Resume обратна връзка или подкрепа. Ето някои от съобщенията, които получих. Ако имате някаква обратна връзка, не се колебайте да ми изпратите имейл на адрес <0>{email}</0>."

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:83
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:53
msgid "Icon"
msgstr "Икона"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:47
msgid "If this app has helped you with your job hunt, let me know by reaching out through <0>this contact form</0>."
msgstr "Ако това приложение ви е помогнало в търсенето на работа, уведомете ме, като се свържете с мен чрез <0>тази форма за контакт</0>."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:128
msgid "If you disable two-factor authentication, you will no longer be required to enter a verification code when logging in."
msgstr "Ако деактивирате двуфакторното удостоверяване, вече няма да се изисква да въвеждате код за проверка при влизане в системата."

#: apps/client/src/pages/home/<USER>/support/index.tsx:59
msgid "If you're multilingual, we'd love your help in bringing the app to more languages and communities. Don't worry if you don't see your language on the list - just give me a shout-out on GitHub, and I'll make sure to include it. Ready to get started? Jump into translation over at Crowdin by clicking the link below."
msgstr "Ако владеете няколко езика, ще се радваме да ни помогнете да разпространим приложението на повече езици и общности. Не се притеснявайте, ако не виждате своя език в списъка - просто ми кажете в GitHub и аз ще се погрижа да го включа. Готови ли сте да започнете? Започнете да превеждате в Crowdin, като кликнете върху връзката по-долу."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:309
msgid "Import"
msgstr "Внос"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:208
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:17
msgid "Import an existing resume"
msgstr "Импортиране на съществуваща автобиография"

#: apps/client/src/components/ai-actions.tsx:85
msgid "Improve Writing"
msgstr "Подобряване на писането"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:188
msgid "In case you are unable to scan this QR Code, you can also copy-paste this link into your authenticator app."
msgstr "В случай че не можете да сканирате този QR код, можете също така да копирате и поставите тази връзка в приложението си за удостоверяване на автентичност."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:67
msgid "In this section, you can change your password and enable/disable two-factor authentication."
msgstr "В този раздел можете да промените паролата си и да активирате/деактивирате двуфакторното удостоверяване."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:64
msgid "In this section, you can delete your account and all the data associated to your user, but please keep in mind that <0>this action is irreversible</0>."
msgstr "В този раздел можете да изтриете профила си и всички данни, свързани с него, но имайте предвид, че <0>това действие е необратимо</0>."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:135
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:116
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:117
msgid "Information"
msgstr "Информация"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:39
msgid "Institution"
msgstr "Институция"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:53
msgid "Issuer"
msgstr "Емитент"

#: apps/client/src/services/errors/translate-error.ts:7
msgid "It doesn't look like a user exists with the credentials you provided."
msgstr "Изглежда, че не съществува потребител с предоставените от вас данни."

#: apps/client/src/services/errors/translate-error.ts:37
msgid "It looks like the backup code you provided is invalid or used. Please try again."
msgstr "Изглежда, че предоставеният от вас код за резервно копие е невалиден или използван. Моля, опитайте отново."

#: apps/client/src/services/errors/translate-error.ts:19
msgid "It looks like the reset token you provided is invalid. Please try restarting the password reset process again."
msgstr "Изглежда, че предоставеният от вас токън за нулиране е невалиден. Моля, опитайте да рестартирате процеса на нулиране на паролата отново."

#: apps/client/src/services/errors/translate-error.ts:46
msgid "It looks like the resume you're looking for doesn't exist."
msgstr "Изглежда, че автобиографията, която търсите, не съществува."

#: apps/client/src/services/errors/translate-error.ts:34
msgid "It looks like the two-factor authentication code you provided is invalid. Please try again."
msgstr "Изглежда, че кодът за двуфакторно удостоверяване, който сте предоставили, е невалиден. Моля, опитайте отново."

#: apps/client/src/services/errors/translate-error.ts:22
msgid "It looks like the verification token you provided is invalid. Please try restarting the verification process again."
msgstr "Изглежда, че предоставеният от вас токен за проверка е невалиден. Моля, опитайте да рестартирате процеса на проверка отново."

#: apps/client/src/services/errors/translate-error.ts:25
msgid "It looks like your email address has already been verified."
msgstr "Изглежда, че имейл адресът ви вече е бил проверен."

#: apps/client/src/pages/auth/register/page.tsx:101
msgctxt "Localized version of a placeholder name. For example, Max Mustermann in German or Jan Kowalski in Polish."
msgid "John Doe"
msgstr "Джон Доу"

#: apps/client/src/pages/auth/register/page.tsx:123
msgctxt "Localized version of a placeholder username. For example, max.mustermann in German or jan.kowalski in Polish."
msgid "john.doe"
msgstr "john.doe"

#: apps/client/src/pages/auth/register/page.tsx:145
msgctxt "Localized version of a placeholder email. For example, <EMAIL> in <NAME_EMAIL> in Polish."
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:54
msgid "JSON"
msgstr "JSON"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:63
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:109
msgid "Keywords"
msgstr "Ключови думи"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:42
#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:52
msgid "Label"
msgstr "Етикет"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:101
msgid "Language"
msgstr "Език"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:83
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:139
msgid "Last updated {lastUpdated}"
msgstr "Последна актуализация {lastUpdated}"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:72
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:197
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:198
msgid "Layout"
msgstr "Макет"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:38
msgid "Learn more"
msgstr "Научете повече"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:44
msgid "Letter"
msgstr "Писмо"

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:64
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:77
msgid "Level"
msgstr "Ниво"

#: apps/client/src/components/copyright.tsx:16
msgid "Licensed under <0>MIT</0>"
msgstr "Лицензиран под <0>MIT</0>"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:86
msgid "Light"
msgstr "Светлина"

#: apps/client/src/pages/home/<USER>/features/index.tsx:69
msgid "Light or dark theme"
msgstr "Светла или тъмна тема"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:165
msgid "Line Height"
msgstr "Височина на линията"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:22
msgid "LinkedIn, JSON Resume, etc."
msgstr "LinkedIn, JSON Resume и др."

#: apps/client/src/pages/dashboard/resumes/page.tsx:47
msgid "List"
msgstr "Списък"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:86
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:81
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:93
msgid "Location"
msgstr "Местоположение"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:51
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:166
msgid "Lock"
msgstr "Заключване"

#: apps/client/src/pages/home/<USER>/features/index.tsx:64
msgid "Lock a resume to prevent editing"
msgstr "Заключване на резюме, за да предотвратите редактирането му"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:43
msgid "Locking a resume will prevent any further changes to it. This is useful when you have already shared your resume with someone and you don't want to accidentally make any changes to it."
msgstr "Заключването на дадено резюме ще предотврати всякакви по-нататъшни промени в него. Това е полезно, когато вече сте споделили автобиографията си с някого и не искате случайно да направите промени в нея."

#: apps/client/src/components/user-options.tsx:38
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:23
msgid "Logout"
msgstr "Излизане от системата"

#: apps/client/src/pages/auth/verify-otp/page.tsx:64
msgid "Lost your device?"
msgstr "Загубили сте устройството си?"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:247
msgid "Main"
msgstr "Основен"

#: apps/client/src/pages/home/<USER>/features/index.tsx:59
msgid "Manage multiple resumes"
msgstr "Управление на множество автобиографии"

#. The month and year should be uniform across all languages.
#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:71
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:69
msgid "March 2023"
msgstr "март 2023 г."

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:112
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:74
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:103
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:69
msgid "March 2023 - Present"
msgstr "март 2023 - Настояще"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:50
msgid "Margin"
msgstr "Марж"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:158
msgid "Max Tokens"
msgstr "Максимален брой жетони"

#: apps/client/src/pages/home/<USER>/features/index.tsx:48
msgid "MIT License"
msgstr "Лиценз MIT"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:145
msgid "Model"
msgstr "Модел"

#: apps/client/src/pages/auth/register/page.tsx:98
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:59
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:48
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:36
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:49
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:88
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:153
msgid "Name"
msgstr "Име"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:39
msgctxt "Name of the Certification"
msgid "Name"
msgstr "Име"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:40
msgid "Network"
msgstr "Мрежа"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:96
msgid "New Password"
msgstr "Нова парола"

#: apps/client/src/components/locale-combobox.tsx:45
msgid "No results found"
msgstr "Не са намерени резултати"

#: apps/client/src/pages/home/<USER>/features/index.tsx:49
msgid "No user tracking or advertising"
msgstr "Без проследяване на потребителите или рекламиране"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:133
msgid "Note: This will make your account less secure."
msgstr "Забележка: Това ще намали сигурността на акаунта ви."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:128
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:16
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:17
msgid "Notes"
msgstr "Бележки"

#: apps/client/src/pages/auth/verify-otp/page.tsx:82
msgid "One-Time Password"
msgstr "Еднократна парола"

#: apps/client/src/components/ai-actions.tsx:56
#: apps/client/src/libs/axios.ts:30
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:188
#: apps/client/src/services/resume/print.tsx:26
msgid "Oops, the server returned an error."
msgstr "Упс, сървърът върна грешка."

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:97
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:77
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:148
msgid "Open"
msgstr "Отворете"

#: apps/client/src/pages/home/<USER>/features/index.tsx:47
msgid "Open Source"
msgstr "Отворен код"

#: apps/client/src/services/openai/change-tone.ts:35
#: apps/client/src/services/openai/fix-grammar.ts:33
#: apps/client/src/services/openai/improve-writing.ts:33
msgid "OpenAI did not return any choices for your text."
msgstr "OpenAI не върна никакви варианти за вашия текст."

#: apps/client/src/pages/home/<USER>/features/index.tsx:52
msgid "OpenAI Integration"
msgstr "Интеграция на OpenAI"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:119
msgid "OpenAI/Ollama API Key"
msgstr "Ключ за API на OpenAI/Ollama"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:79
msgid "OpenAI/Ollama Integration"
msgstr "Интеграция на OpenAI/Ollama"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:67
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:182
msgid "Options"
msgstr "Опции"

#: apps/client/src/pages/auth/layout.tsx:47
msgctxt "The user can either login with email/password, or continue with GitHub or Google."
msgid "or continue with"
msgstr "или да продължите с"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:39
msgid "Organization"
msgstr "Организация"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:100
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:25
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:26
msgid "Page"
msgstr "Страница"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:228
msgid "Page {pageNumber}"
msgstr "Страница {pageNumber}"

#: apps/client/src/pages/auth/login/page.tsx:110
#: apps/client/src/pages/auth/register/page.tsx:163
#: apps/client/src/pages/auth/reset-password/page.tsx:83
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:73
msgid "Password"
msgstr "Парола"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:72
msgid "PDF"
msgstr "PDF"

#: apps/client/src/pages/home/<USER>/features/index.tsx:63
msgid "Personal notes for each resume"
msgstr "Лични бележки за всяко резюме"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:81
msgid "Phone"
msgstr "Телефон"

#: apps/client/src/pages/auth/layout.tsx:76
msgid "Photograph by Patrick Tomasso"
msgstr "Снимка: Патрик Томасо"

#: apps/client/src/pages/home/<USER>/features/index.tsx:66
msgid "Pick any font from Google Fonts"
msgstr "Изберете произволен шрифт от Google Fonts"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/section.tsx:69
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:121
msgid "Picture"
msgstr "Снимка"

#: apps/client/src/pages/auth/verify-email/page.tsx:59
msgid "Please note that this step is completely optional."
msgstr "Моля, обърнете внимание, че тази стъпка не е задължителна."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:225
msgid "Please select a file type"
msgstr "Моля, изберете тип файл"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:228
msgid "Please store your backup codes in a secure location. You can use one of these one-time use codes to login in case you lose access to your authenticator app."
msgstr "Моля, съхранявайте резервните си кодове на сигурно място. Можете да използвате един от тези кодове за еднократна употреба, за да влезете в системата, в случай че загубите достъп до приложението си за удостоверяване."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:99
msgid "Portrait"
msgstr "Портрет"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:54
msgctxt "Position held at a company, for example, Software Engineer"
msgid "Position"
msgstr "Позиция"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:53
msgid "Position"
msgstr "Позиция"

#: apps/client/src/pages/home/<USER>/features/index.tsx:96
msgid "Powered by"
msgstr "Задвижвано от"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:94
msgid "Powered by <0>Simple Icons</0>"
msgstr "Осъществено от <0>Simple Icons</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:43
msgid "Primary Color"
msgstr "Основен цвят"

#: apps/client/src/pages/home/<USER>/footer.tsx:50
msgid "Privacy Policy"
msgstr "Политика за поверителност"

#: apps/client/src/components/ai-actions.tsx:109
#: apps/client/src/components/ai-actions.tsx:112
msgid "Professional"
msgstr "Професионален"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:66
msgid "Profile"
msgstr "Профил"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:55
msgid "Public"
msgstr "Публичен"

#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:53
msgid "Publisher"
msgstr "Издател"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:69
msgid "Raise an issue"
msgstr "Повдигане на въпрос"

#: apps/client/src/components/copyright.tsx:35
#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/builder/page.tsx:60
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/home/<USER>/footer.tsx:18
#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/public/page.tsx:74
#: apps/client/src/pages/public/page.tsx:95
msgid "Reactive Resume"
msgstr "Реактивна автобиография"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:39
msgid "Reactive Resume has helped people land jobs at these great companies:"
msgstr "Reactive Resume е помогнал на хора да намерят работа в тези големи компании:"

#: apps/client/src/pages/home/<USER>/support/index.tsx:12
msgid "Reactive Resume is a free and open-source project crafted mostly by me, and your support would be greatly appreciated. If you're inclined to contribute, and only if you can afford to, consider making a donation through any of the listed platforms. Additionally, donations to Reactive Resume through Open Collective are tax-exempt, as the project is fiscally hosted by Open Collective Europe."
msgstr "Reactive Resume е безплатен проект с отворен код, създаден предимно от мен, и вашата подкрепа ще бъде високо оценена. Ако сте склонни да дадете своя принос, и то само ако можете да си го позволите, помислете за дарение чрез някоя от изброените платформи. Освен това даренията за Reactive Resume чрез Open Collective са освободени от данъци, тъй като проектът се поддържа финансово от Open Collective Europe."

#: apps/client/src/pages/home/<USER>/features/index.tsx:107
msgid "Reactive Resume is a passion project of over 3 years of hard work, and with that comes a number of re-iterated ideas and features that have been built to (near) perfection."
msgstr "Reactive Resume е страстен проект, по който се работи повече от 3 години, а с това идват и редица повторени идеи и функции, които са изградени до (почти) съвършенство."

#: apps/client/src/pages/home/<USER>/contributors/index.tsx:22
msgid "Reactive Resume thrives thanks to its vibrant community. This project owes its progress to numerous individuals who've dedicated their time and skills. Below, we celebrate the coders who've enhanced its features on GitHub and the linguists whose translations on Crowdin have made it accessible to a broader audience."
msgstr "Reactive Resume процъфтява благодарение на своята динамична общност. Този проект дължи напредъка си на множество хора, които са посветили времето и уменията си. По-долу отбелязваме кодерите, които са подобрили функциите му в GitHub, и лингвистите, чиито преводи в Crowdin са го направили достъпен за по-широка аудитория."

#: apps/client/src/pages/builder/_components/toolbar.tsx:89
msgid "Redo"
msgstr "Redo"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:100
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:157
msgid "Remove"
msgstr "Премахване на"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:231
msgid "Remove Page"
msgstr "Премахване на страницата"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:111
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:101
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:86
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:152
msgid "Rename"
msgstr "Преименуване на"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:199
msgid "Resend email confirmation link"
msgstr "Повторно изпращане на връзка за потвърждение на имейл"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:152
msgid "Reset"
msgstr "Нулиране на"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:201
msgid "Reset Layout"
msgstr "Нулиране на оформлението"

#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/reset-password/page.tsx:65
msgid "Reset your password"
msgstr "Възстановяване на паролата ви"

#: apps/client/src/pages/builder/_components/toolbar.tsx:124
msgid "Reset Zoom"
msgstr "Нулиране на мащаба"

#: apps/client/src/pages/dashboard/_components/sidebar.tsx:86
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/resumes/page.tsx:37
msgid "Resumes"
msgstr "Резюмета"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:14
msgid "Resumes Generated"
msgstr "Генерирани автобиографии"

#: apps/client/src/pages/home/<USER>/features/index.tsx:105
msgid "Rich in features, not in pricing."
msgstr "Богат на функции, но не и на цени."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:138
msgid "Rounded"
msgstr "Заоблени"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:180
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:245
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:217
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:131
msgid "Save Changes"
msgstr "Запазване на промените"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Save Locally"
msgstr "Запазване на местно ниво"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Saved"
msgstr "Запазено"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:168
msgid "Scan the QR code below with your authenticator app to setup 2FA on your account."
msgstr "Сканирайте QR кода по-долу с приложението си за удостоверяване, за да настроите 2FA в акаунта си."

#. Score or honors for the degree, for example, CGPA or magna cum laude
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:92
msgid "Score"
msgstr "Резултат"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Pan"
msgstr "Превъртете към Pan"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Zoom"
msgstr "Превъртете, за да увеличите мащаба"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:111
msgid "Search for a font family"
msgstr "Търсене на семейство шрифтове"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:126
msgid "Search for a font subset"
msgstr "Търсене на подмножество на шрифт"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:139
msgid "Search for a font variant"
msgstr "Търсене на вариант на шрифт"

#: apps/client/src/components/locale-combobox.tsx:41
msgid "Search for a language"
msgstr "Търсене на език"

#: apps/client/src/pages/home/<USER>/features/index.tsx:56
msgid "Secure with two-factor authentication"
msgstr "Сигурност с двуфакторно удостоверяване"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:65
msgid "Security"
msgstr "Защита"

#: apps/client/src/pages/home/<USER>/features/index.tsx:50
msgid "Self-host with Docker"
msgstr "Самостоятелно хостване с Docker"

#: apps/client/src/pages/auth/forgot-password/page.tsx:104
msgid "Send Email"
msgstr "Изпращане на имейл"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:74
msgid "Send me a message"
msgstr "Изпратете ми съобщение"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:97
msgid "Separate Links"
msgstr "Отделни връзки"

#: apps/client/src/components/user-options.tsx:32
#: apps/client/src/pages/dashboard/_components/sidebar.tsx:92
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/dashboard/settings/page.tsx:26
msgid "Settings"
msgstr "Настройки"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:159
msgid "Setup two-factor authentication on your account"
msgstr "Настройка на двуфакторно удостоверяване в акаунта ви"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:107
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:38
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:39
msgid "Sharing"
msgstr "Споделяне на"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Show"
msgstr "Покажи"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:78
msgid "Show Break Line"
msgstr "Показване на линия на прекъсване"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:91
msgid "Show Page Numbers"
msgstr "Показване на номера на страници"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:248
msgid "Sidebar"
msgstr "Странична лента"

#: apps/client/src/pages/auth/backup-otp/page.tsx:103
#: apps/client/src/pages/auth/login/page.tsx:127
#: apps/client/src/pages/auth/verify-otp/page.tsx:92
msgid "Sign in"
msgstr "Влезте"

#: apps/client/src/pages/auth/register/page.tsx:74
msgid "Sign in now"
msgstr "Влезте сега"

#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/login/page.tsx:60
msgid "Sign in to your account"
msgstr "Влезте в профила си"

#: apps/client/src/pages/home/<USER>/features/index.tsx:55
msgid "Sign in with Email"
msgstr "Влезте с имейл"

#: apps/client/src/pages/home/<USER>/features/index.tsx:53
msgid "Sign in with GitHub"
msgstr "Влезте в GitHub"

#: apps/client/src/pages/home/<USER>/features/index.tsx:54
msgid "Sign in with Google"
msgstr "Влезте в Google"

#: apps/client/src/pages/auth/register/page.tsx:179
msgid "Sign up"
msgstr "Регистрирайте се"

#: apps/client/src/pages/auth/login/page.tsx:74
msgid "Signing in via email is currently disabled by the administrator."
msgstr "Понастоящем администраторът е забранил влизането чрез имейл."

#: apps/client/src/pages/auth/register/page.tsx:82
msgid "Signups are currently disabled by the administrator."
msgstr "В момента регистрациите са забранени от администратора."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:65
msgid "Size (in px)"
msgstr "Размер (в px)"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:228
msgid "Slug"
msgstr "Slug"

#: apps/client/src/services/errors/translate-error.ts:55
msgid "Something went wrong while grabbing a preview your resume. Please try again later or raise an issue on GitHub."
msgstr "Нещо се е объркало, докато сте преглеждали автобиографията си. Моля, опитайте отново по-късно или повдигнете въпрос в GitHub."

#: apps/client/src/services/errors/translate-error.ts:52
msgid "Something went wrong while printing your resume. Please try again later or raise an issue on GitHub."
msgstr "Нещо се е объркало при отпечатването на автобиографията ви. Моля, опитайте отново по-късно или повдигнете въпрос в GitHub."

#: apps/client/src/services/errors/translate-error.ts:58
msgid "Something went wrong while processing your request. Please try again later or raise an issue on GitHub."
msgstr "Нещо се е объркало при обработката на заявката ви. Моля, опитайте отново по-късно или повдигнете въпрос в GitHub."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:87
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:132
msgid "Square"
msgstr "Квадрат"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:23
msgid "Start building from scratch"
msgstr "Започнете да строите от нулата"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:185
msgid "Start building your resume by giving it a name."
msgstr "Започнете да изграждате автобиографията си, като й дадете име."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:114
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:22
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:23
msgid "Statistics"
msgstr "Статистика"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:38
msgid "Statistics are available only for public resumes."
msgstr "Статистическите данни са налични само за публичните автобиографии."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:162
msgid "Store your backup codes securely"
msgstr "Съхранявайте сигурно кодовете си за архивиране"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:138
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:114
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:109
msgid "Summary"
msgstr "Резюме"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:18
msgid "Support the app by donating what you can!"
msgstr "Подкрепете приложението, като дарите каквото можете!"

#: apps/client/src/pages/home/<USER>/support/index.tsx:9
msgid "Supporting Reactive Resume"
msgstr "Поддържане на реактивно резюме"

#: apps/client/src/pages/home/<USER>/features/index.tsx:65
msgid "Supports A4/Letter page formats"
msgstr "Поддържа формати на страници A4/Letter"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:85
msgid "System"
msgstr "Система"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:65
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:18
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:19
msgid "Template"
msgstr "Шаблон"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:9
msgid "Templates"
msgstr "Шаблони"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:68
msgid "Testimonials"
msgstr "Отзиви"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:103
msgid "Text Color"
msgstr "Цвят на текста"

#: apps/client/src/pages/public/error.tsx:17
msgid "The page you're looking for doesn't exist."
msgstr ""

#: apps/client/src/pages/public/error.tsx:29
msgid "The request was invalid."
msgstr ""

#: apps/client/src/services/errors/translate-error.ts:49
msgid "The resume you want to update is locked, please unlock if you wish to make any changes to it."
msgstr "Автобиографията, която искате да актуализирате, е заключена, моля, отключете я, ако искате да направите промени в нея."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:86
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:19
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:20
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:79
msgid "Theme"
msgstr "Тема"

#: apps/client/src/services/errors/translate-error.ts:40
msgid "There was an error connecting to the browser. Please make sure 'chrome' is running and reachable."
msgstr "Имаше грешка при свързването с браузъра. Моля, уверете се, че \"chrome\" е стартиран и достъпен."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:130
msgid "This action can be reverted by clicking on the undo button in the floating toolbar."
msgstr "Това действие може да бъде върнато, като щракнете върху бутона Отмяна в плаващата лента с инструменти."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:151
msgid "This action cannot be undone. This will permanently delete your resume and cannot be recovered."
msgstr "Това действие не може да бъде отменено. Това ще доведе до окончателно изтриване на автобиографията ви и няма да може да бъде възстановена."

#: apps/client/src/services/errors/translate-error.ts:16
msgid "This email address is associated with an OAuth account. Please sign in with your OAuth provider."
msgstr "Този имейл адрес е свързан с акаунт в OAuth. Моля, влезте с вашия доставчик на OAuth."

#: apps/client/src/pages/builder/_components/header.tsx:57
msgid "This resume is locked, please unlock to make further changes."
msgstr "Това резюме е заключено, моля, отключете го, за да направите допълнителни промени."

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:23
msgid "This section is reserved for your personal notes specific to this resume. The content here remains private and is not shared with anyone else."
msgstr "Този раздел е запазен за личните ви бележки, свързани с тази автобиография. Съдържанието тук остава лично и не се споделя с никого другиго."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:216
msgid "Tip: You can name the resume referring to the position you are applying for."
msgstr "Съвет: Можете да дадете име на автобиографията в зависимост от позицията, за която кандидатствате."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:39
msgctxt "Name of the Award"
msgid "Title"
msgstr "Заглавие"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:196
msgid "Title"
msgstr "Заглавие"

#: apps/client/src/pages/builder/_components/toolbar.tsx:138
msgid "Toggle Page Break Line"
msgstr "Превключване на линията за прекъсване на страницата"

#: apps/client/src/pages/builder/_components/toolbar.tsx:150
msgid "Toggle Page Numbers"
msgstr "Превключване на номера на страници"

#: apps/client/src/pages/home/<USER>/features/index.tsx:68
msgid "Track views and downloads"
msgstr "Проследяване на изгледи и изтегляния"

#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/auth/verify-otp/page.tsx:57
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:129
msgid "Two-Factor Authentication"
msgstr "Двуфакторно удостоверяване на автентичността"

#: apps/client/src/services/errors/translate-error.ts:31
msgid "Two-factor authentication is already enabled for this account."
msgstr "Двуфакторното удостоверяване вече е активирано за този акаунт."

#: apps/client/src/services/errors/translate-error.ts:28
msgid "Two-factor authentication is not enabled for this account."
msgstr "Двуфакторното удостоверяване не е активирано за този акаунт."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:84
msgid "Type <0>delete</0> to confirm deleting your account."
msgstr "Въведете <0>Delete</0>, за да потвърдите изтриването на акаунта си."

#. For example, Bachelor's Degree or Master's Degree
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:54
msgid "Type of Study"
msgstr "Вид на изследването"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:79
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:76
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:77
msgid "Typography"
msgstr "Типография"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:203
msgid "Underline Links"
msgstr "Подчертаване на връзки"

#: apps/client/src/pages/builder/_components/toolbar.tsx:76
msgid "Undo"
msgstr "Отмяна на"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:52
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:110
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:161
msgid "Unlock"
msgstr "Отключване на"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:44
msgid "Unlocking a resume will allow you to make changes to it again."
msgstr "Отключването на автобиографията ще ви позволи отново да правите промени в нея."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Unverified"
msgstr "Непроверен"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:164
msgid "Update an existing item"
msgstr "Актуализиране на съществуващ елемент"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:179
msgid "Update an existing resume"
msgstr "Актуализиране на съществуваща автобиография"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:212
msgid "Upload a file from one of the accepted sources to parse existing data and import it into Reactive Resume for easier editing."
msgstr "Качете файл от един от приетите източници, за да анализирате съществуващите данни и да ги импортирате в Reactive Resume за по-лесно редактиране."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:73
msgid "URL"
msgstr "URL"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:61
msgid "URL must start with https://"
msgstr "URL адресът трябва да започва с https://"

#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/backup-otp/page.tsx:57
msgid "Use your backup code"
msgstr "Използване на резервен код"

#: apps/client/src/services/errors/translate-error.ts:13
msgid "User does not have an associated 'secrets' record. Please report this issue on GitHub."
msgstr "Потребителят няма свързан запис \"secrets\". Моля, докладвайте този проблем в GitHub."

#: apps/client/src/pages/auth/register/page.tsx:119
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:55
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:167
msgid "Username"
msgstr "Потребителско име"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:13
msgid "Users Signed Up"
msgstr "Регистрирани потребители"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:296
msgid "Validate"
msgstr "Удостоверяване на"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:314
msgid "Validated"
msgstr "Утвърден"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:97
msgid "Value"
msgstr "Стойност"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Verified"
msgstr "Проверено"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:161
msgid "Verify that two-factor authentication has been setup correctly"
msgstr "Проверете дали двуфакторното удостоверяване е настроено правилно"

#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-email/page.tsx:48
msgid "Verify your email address"
msgstr "Потвърдете имейл адреса си"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:26
msgid "Version 4"
msgstr "Версия 4"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:51
msgid "Views"
msgstr "Прегледи"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:87
msgid "Visible"
msgstr "Видим"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:70
msgid "Visit <0>Phosphor Icons</0> for a list of available icons"
msgstr "Посетете <0>Phosphor Icons</0> за списък на наличните икони"

#: apps/client/src/pages/auth/verify-email/page.tsx:61
msgid "We verify your email address only to ensure that we can send you a password reset link in case you forget your password."
msgstr "Проверяваме имейл адреса ви само за да можем да ви изпратим връзка за възстановяване на паролата, в случай че сте я забравили."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:124
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:100
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:95
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:69
msgid "Website"
msgstr "Уебсайт"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:32
msgid "What's new in the latest version"
msgstr "Какво е новото в последната версия"

#: apps/client/src/pages/public/error.tsx:26
msgid "You are not authorized to access this page."
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:68
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:114
msgid "You can add multiple keywords by separating them with a comma or pressing enter."
msgstr "Можете да добавите няколко ключови думи, като ги разделите със запетая или натиснете Enter."

#: apps/client/src/pages/auth/login/page.tsx:99
msgid "You can also enter your username."
msgstr "Можете да въведете и потребителското си име."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:103
msgid "You can also integrate with Ollama simply by setting the API key to `sk-1234567890abcdef` and the Base URL to your Ollama URL, i.e. `http://localhost:11434/v1`. You can also pick and choose models and set the max tokens as per your preference."
msgstr "Можете също така да се интегрирате с Ollama, като просто зададете API ключа на `sk-1234567890abcdef`, а базовия URL адрес - на вашия URL адрес на Ollama, т.е. `http://localhost:11434/v1`. Можете също така да избирате модели и да задавате максималните токени според предпочитанията си."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:81
msgid "You can make use of the OpenAI API to help you generate content, or improve your writing while composing your resume."
msgstr "Можете да използвате приложния програмен интерфейс на OpenAI, за да генерирате съдържание, или да подобрите писането, докато съставяте автобиографията си."

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:40
msgid "You can track the number of views your resume has received, or how many people have downloaded the resume by enabling public sharing."
msgstr "Можете да проследите броя на прегледите на автобиографията си или колко души са я изтеглили, като разрешите публичното споделяне."

#: apps/client/src/pages/public/error.tsx:20
msgid "You don't have permission to access this page."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:87
msgid "You have the option to <0>obtain your own OpenAI API key</0>. This key empowers you to leverage the API as you see fit. Alternatively, if you wish to disable the AI features in Reactive Resume altogether, you can simply remove the key from your settings."
msgstr "Имате възможност да <0>получите собствен API ключ на OpenAI</0>. Този ключ ви дава възможност да използвате API по ваша преценка. Ако желаете да деактивирате функциите на изкуствения интелект в Reactive Resume, можете просто да премахнете ключа от настройките си."

#: apps/client/src/pages/auth/verify-email/page.tsx:50
msgid "You should have received an email from <0>Reactive Resume</0> with a link to verify your account."
msgstr "Трябва да сте получили имейл от <0>Reactive Resume</0> с връзка за потвърждаване на профила ви."

#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/forgot-password/page.tsx:54
msgid "You've got mail!"
msgstr "Имате поща!"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:52
msgid "Your account and all your data has been deleted successfully. Goodbye!"
msgstr "Вашият акаунт и всички ваши данни са изтрити успешно. Сбогом!"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:191
msgid "Your API key is securely stored in the browser's local storage and is only utilized when making requests to OpenAI via their official SDK. Rest assured that your key is not transmitted to any external server except when interacting with OpenAI's services."
msgstr "Вашият API ключ се съхранява по сигурен начин в локалното хранилище на браузъра и се използва само при извършване на заявки към OpenAI чрез официалния им SDK. Бъдете сигурни, че вашият ключ не се предава на външен сървър, освен при взаимодействие с услугите на OpenAI."

#: apps/client/src/pages/auth/verify-email/page.tsx:28
msgid "Your email address has been verified successfully."
msgstr "Имейл адресът ви е проверен успешно."

#: apps/client/src/services/openai/client.ts:11
msgid "Your OpenAI API Key has not been set yet. Please go to your account settings to enable OpenAI Integration."
msgstr "Вашият API ключ на OpenAI все още не е зададен. Моля, отидете в настройките на профила си, за да разрешите интеграцията с OpenAI."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:56
msgid "Your password has been updated successfully."
msgstr "Вашата парола е актуализирана успешно."

#: apps/client/src/pages/builder/_components/toolbar.tsx:112
msgid "Zoom In"
msgstr "Увеличаване на мащаба"

#: apps/client/src/pages/builder/_components/toolbar.tsx:118
msgid "Zoom Out"
msgstr "Увеличаване на мащаба"

