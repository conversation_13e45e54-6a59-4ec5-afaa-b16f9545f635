copy the environment file:
$ cp .env.example .env

Start the required services (PostgreSQL, Minio, Chrome):
$ docker compose -f compose.dev.yml up -d

$ node --version && pnpm --version

Install pnpm
npm install -g pnpm

Build the project 
pnpm build
-build will be created in dist/ directory

run the database migrations:
pnpm prisma:migrate

start the development server:
pnpm dev


Dependencies:
Docker
docker compose
nvm 
node


----------------------------------------------------------------------------------------------------
  ➜  Local:   http://localhost:6173/artboard/
  ➜  Network: http://************:6173/artboard/
  ➜  Network: http://**********:6173/artboard/
Loading proxy configuration from: /var/www/html/python/Reactive-Resume/apps/client/proxy.conf.json
  ➜  Local:   http://localhost:5173/
  ➜  Network: http://************:5173/
  ➜  Network: http://**********:5173/



---------------------------------------------------------------------------------
Running Services:
Frontend (Client): http://localhost:5173/
Artboard: http://localhost:6173/artboard/
Backend (Server): http://localhost:3000 (API)
PostgreSQL Database: localhost:5432
Minio Storage: http://localhost:9000
Minio Console: http://localhost:9001
Adminer (Database Management): http://localhost:5555
Chrome Browser Service: localhost:8080
Main Application URLs:
Primary Application: http://localhost:5173/
API Documentation: http://localhost:3000/api (if Swagger is enabled)
What's Running:
✅ PostgreSQL - Database for storing user data and resumes
✅ Minio - Object storage for images and files
✅ Chrome/Browserless - For PDF generation and previews
✅ NestJS Backend - API server running on port 3000
✅ React Frontend - Client application running on port 5173
✅ Artboard - Resume preview/editing interface on port 6173
The application is ready to use! You can now:

Visit http://localhost:5173/ to access the main application
Create an account and start building resumes
Use the database management interface at http://localhost:5555 if needed
Access the Minio console at http://localhost:9001 for file storage management
All services are healthy and the application should be fully functional for creating, editing, and managing resumes.