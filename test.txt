copy the environment file:
$ cp .env.example .env

Start the required services (PostgreSQL, Minio, Chrome):
$ docker compose -f compose.dev.yml up -d

$ node --version && pnpm --version

Install pnpm
npm install -g pnpm

Build the project 
pnpm build
-build will be created in dist/ directory

run the database migrations:
pnpm prisma:migrate

start the development server:
pnpm dev


Dependencies:
Docker
docker compose
nvm 
node


----------------------------------------------------------------------------------------------------
  ➜  Local:   http://localhost:6173/artboard/
  ➜  Network: http://************:6173/artboard/
  ➜  Network: http://**********:6173/artboard/
Loading proxy configuration from: /var/www/html/python/Reactive-Resume/apps/client/proxy.conf.json
  ➜  Local:   http://localhost:5173/
  ➜  Network: http://************:5173/
  ➜  Network: http://**********:5173/



---------------------------------------------------------------------------------
What's Working:
✅ Docker services are running (PostgreSQL, Minio, Chrome, Adminer)
✅ Dependencies installed successfully
✅ Prisma client generated
✅ Database migrations applied
✅ Frontend applications are running and accessible
Next Steps to Fix the Server:
The server issue is likely due to the Node.js version mismatch (project requires >=22.13.1, but we have 18.16.0). To fully resolve this, you could:

Upgrade Node.js to version 22.13.1 or higher
Use Docker to run the complete application with the correct Node.js version
Fix the module compatibility issue in the codebase
For now, you can explore the frontend interface at http://localhost:5173, though some backend-dependent features may not work without the server running.

Would you like me to help you fix the server issue or would you prefer to explore the frontend first?
------------------------------------------------------------------------------------
have installed the latest node version, run the project and fix the issues