{"name": "client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/client/src", "projectType": "application", "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "dependsOn": ["^build"], "options": {"outputPath": "dist/apps/client"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "client:build", "proxyConfig": "apps/client/proxy.conf.json"}, "configurations": {"development": {"buildTarget": "client:build:development", "hmr": true}, "production": {"buildTarget": "client:build:production", "hmr": false}}}, "preview": {"executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "client:build"}, "configurations": {"development": {"buildTarget": "client:build:development"}, "production": {"buildTarget": "client:build:production"}}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/apps/client"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/client/**/*.{ts,tsx,js,jsx}"]}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "client:build"}}}, "tags": ["frontend"]}