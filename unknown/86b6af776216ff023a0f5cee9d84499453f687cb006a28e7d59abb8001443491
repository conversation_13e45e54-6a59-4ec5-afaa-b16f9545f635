msgid ""
msgstr ""
"POT-Creation-Date: 2023-11-10 13:15+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ms\n"
"Project-Id-Version: reactive-resume\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-02-03 09:13\n"
"Last-Translator: \n"
"Language-Team: Malay\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: reactive-resume\n"
"X-Crowdin-Project-ID: 503410\n"
"X-Crowdin-Language: ms\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 494\n"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:171
msgid "You have enabled two-factor authentication successfully."
msgstr "Anda telah berjaya mengaktifkan pengesahan dua faktor."

#: apps/client/src/pages/home/<USER>/features/index.tsx:57
msgid "{templatesCount} resume templates to choose from"
msgstr "{templatesCount} templat resume yang dapat dipilih"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:142
msgid "{value, plural, one {Column} other {Columns}}"
msgstr "{value, plural, other {Ruangan}}"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:20
msgid "<0>I built Reactive Resume mostly by myself during my spare time, with a lot of help from other great open-source contributors.</0><1>If you like the app and want to support keeping it free forever, please donate whatever you can afford to give.</1>"
msgstr "<0>Saya membina Reaktif Resume kebanyakannya sendiri semasa masa lapang saya, dengan banyak bantuan dari penyumbang sumber terbuka yang hebat lain.</0><1>Jika anda menyukai aplikasi ini dan ingin menyokong untuk memastikan ia kekal percuma selamanya, sila derma apa sahaja yang anda mampu untuk memberi.</1>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:51
msgid "<0>I'm sure the app is not perfect, but I'd like for it to be.</0><1>If you faced any issues while creating your resume, or have an idea that would help you and other users in creating your resume more easily, drop an issue on the repository or send me an email about it.</1>"
msgstr "<0>Saya yakin aplikasi ini tidak sempurna, tetapi saya ingin ia menjadi sempurna.</0><1>Jika anda menghadapi sebarang isu semasa membuat resume anda, atau mempunyai idea yang boleh membantu anda dan pengguna lain dalam membuat resume anda dengan lebih mudah, sila berikan isu di repository atau hantar saya emel mengenainya.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:201
msgid "<0>Note: </0>By utilizing the OpenAI API, you acknowledge and accept the <1>terms of use</1> and <2>privacy policy</2> outlined by OpenAI. Please note that Reactive Resume bears no responsibility for any improper or unauthorized utilization of the service, and any resulting repercussions or liabilities solely rest on the user."
msgstr "<0>Nota: </0>Dengan menggunakan API OpenAI, anda mengakui dan menerima <1>terma penggunaan</1> dan <2>dasar privasi</2> yang digariskan oleh OpenAI. Sila ambil perhatian bahawa Reactive Resume tidak bertanggungjawab atas sebarang penggunaan perkhidmatan yang tidak betul atau tidak dibenarkan, dan sebarang akibat atau liabiliti yang timbul semata-mata bergantung kepada pengguna."

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:85
msgid "<0>The community has spent a lot of time writing the documentation for Reactive Resume, and I'm sure it will help you get started with the app.</0><1>There are also a lot of examples to help you get started, and features that you might not know about which could help you build your perfect resume.</1>"
msgstr "<0>Masyarakat telah banyak menghabiskan masa menulis dokumentasi untuk Resume Reaktif, dan saya yakin ia akan membantu anda memulakan dengan aplikasi.</0><1>Juga terdapat banyak contoh untuk membantu anda memulakan, dan ciri-ciri yang mungkin anda tidak tahu tentang yang boleh membantu anda membina resume yang sempurna.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:140
msgid "<0>Two-factor authentication is currently disabled.</0> You can enable it by adding an authenticator app to your account."
msgstr "<0>Pengesahan dua faktor kini dimatikan.</0> Anda boleh mengaktifkannya dengan menambah aplikasi pengesah ke akaun anda."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:133
msgid "<0>Two-factor authentication is enabled.</0> You will be asked to enter a code every time you sign in."
msgstr "<0>Pengesahan dua faktor diaktifkan.</0> Anda akan diminta untuk memasukkan kod setiap kali anda log masuk."

#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/home/<USER>/hero/index.tsx:40
msgid "A free and open-source resume builder"
msgstr "Pembina resume Percuma dan sumber terbuka"

#: apps/client/src/pages/home/<USER>/footer.tsx:21
#: apps/client/src/pages/home/<USER>/hero/index.tsx:45
msgid "A free and open-source resume builder that simplifies the process of creating, updating, and sharing your resume."
msgstr "Sebuah pembina resume percuma dan sumber terbuka yang menyederhanakan proses mencipta, mengemaskini, dan berkongsi resume anda."

#: apps/client/src/pages/builder/_components/toolbar.tsx:59
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:29
msgid "A link has been copied to your clipboard."
msgstr "Pautan telah disalin ke papan keratan anda."

#: apps/client/src/components/copyright.tsx:29
msgid "A passion project by <0>Amruth Pillai</0>"
msgstr "Sebuah projek yang digerakkan oleh <0>Amruth Pillai</0>"

#: apps/client/src/pages/auth/forgot-password/page.tsx:57
msgid "A password reset link should have been sent to your inbox, if an account existed with the email you provided."
msgstr "Pautan set semula kata laluan sepatutnya telah dihantar ke peti masuk anda, jika akaun wujud dengan e-mel yang anda berikan."

#: apps/client/src/services/errors/translate-error.ts:43
msgid "A resume with this slug already exists, please pick a different unique identifier."
msgstr "Satu resume dengan slug ini sudah wujud, sila pilih pengenal unik yang berbeza."

#: apps/client/src/services/errors/translate-error.ts:10
msgid "A user with this email address and/or username already exists."
msgstr "Seorang pengguna dengan alamat emel dan/atau nama pengguna ini sudah wujud."

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:43
msgid "A4"
msgstr "A4"

#. Helper text to let the user know what filetypes are accepted. {accept} can be .pdf or .json.
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:270
msgid "Accepts only {accept} files"
msgstr "Hanya menerima fail {accept}"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:105
msgid "Account"
msgstr "Akaun"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:175
msgid "Add a custom field"
msgstr "Tambahkan medan tersuai"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:119
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:170
msgctxt "For example, add a new work experience, or add a new profile."
msgid "Add a new item"
msgstr "Tambah item baru"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:91
msgid "Add a new item"
msgstr "Tambah item baru"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:146
#: apps/client/src/pages/builder/sidebars/left/index.tsx:263
msgid "Add a new section"
msgstr "Tambah bahagian baru"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:261
msgid "Add New Page"
msgstr "Tambah Halaman Baru"

#: apps/client/src/components/ai-actions.tsx:79
msgid "AI"
msgstr "AI"

#: apps/client/src/pages/auth/register/page.tsx:71
msgid "Already have an account?"
msgstr "Sudah mempunyai akaun?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:144
msgid "An error occurred while validating the file."
msgstr "Ralat berlaku semasa menyahkan fail."

#: apps/client/src/pages/public/error.tsx:23
msgid "An internal server error occurred."
msgstr ""

#: apps/client/src/pages/public/error.tsx:32
msgid "An unexpected error occurred."
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:134
msgid "and many more..."
msgstr "dan banyak lagi..."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:57
msgid "Anyone with the link can view and download the resume."
msgstr "Sesiapa yang mempunyai pautan boleh melihat dan muat turun resume."

#: apps/client/src/pages/builder/_components/toolbar.tsx:60
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:30
msgid "Anyone with this link can view and download the resume. Share it on your profile or with recruiters."
msgstr "Sesiapa dengan pautan ini boleh melihat dan memuat turun resume. Kongsi pada profil anda atau dengan perekrut."

#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:41
msgid "Apply Custom CSS"
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:128
msgid "Are you sure you want to delete this item?"
msgstr "Adakah anda pasti ingin memadamkan item ini?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:149
msgid "Are you sure you want to delete your resume?"
msgstr "Adakah anda pasti mahu memadam resume anda?"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:125
msgid "Are you sure you want to disable two-factor authentication?"
msgstr "Adakah anda pasti ingin melumpuhkan pengesahan dua faktor?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:38
msgid "Are you sure you want to lock this resume?"
msgstr "Adakah anda pasti mahu mengunci resume ini?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:39
msgid "Are you sure you want to unlock this resume?"
msgstr "Adakah anda pasti ingin buka kunci resume ini?"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Are you sure?"
msgstr "Adakah anda pasti?"

#. For example, Computer Science or Business Administration
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:73
msgid "Area of Study"
msgstr "Kawasan Pengajian"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:79
msgid "Aspect Ratio"
msgstr "Nisbah aspek"

#: apps/client/src/pages/home/<USER>/features/index.tsx:51
msgid "Available in {languagesCount} languages"
msgstr "Boleh didapati dalam {languagesCount} bahasa"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:53
msgid "Awarder"
msgstr "Pemberi Anugerah"

#: apps/client/src/pages/auth/backup-otp/page.tsx:99
#: apps/client/src/pages/auth/forgot-password/page.tsx:100
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:243
msgid "Back"
msgstr "Kembali"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:73
msgid "Background Color"
msgstr "Warna Latar Belakang"

#: apps/client/src/pages/auth/backup-otp/page.tsx:75
msgid "Backup Code"
msgstr "Kod Sandaran"

#: apps/client/src/pages/auth/backup-otp/page.tsx:81
msgid "Backup Codes may contain only lowercase letters or numbers, and must be exactly 10 characters."
msgstr "Kod Sandaran mungkin mengandungi huruf kecil atau nombor sahaja, dan mesti tepat 10 aksara."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:132
msgid "Base URL"
msgstr "Pautan Asas"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:55
msgctxt "The basics section of a resume consists of User's Picture, Full Name, Location etc."
msgid "Basics"
msgstr "Asas"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:21
msgid "Basics"
msgstr "Asas"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:191
msgid "Border"
msgstr "Sempadan"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:124
msgid "Border Radius"
msgstr "Radius Sempadan"

#: apps/client/src/pages/public/page.tsx:93
msgid "Built with"
msgstr "Dibina dengan"

#: apps/client/src/components/copyright.tsx:27
#: apps/client/src/pages/home/<USER>/contributors/index.tsx:20
msgid "By the community, for the community."
msgstr "Oleh komuniti, untuk komuniti."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:135
#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:49
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:156
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:137
msgid "Cancel"
msgstr "Batal"

#: apps/client/src/components/ai-actions.tsx:103
#: apps/client/src/components/ai-actions.tsx:106
msgid "Casual"
msgstr "Kasual"

#: apps/client/src/pages/builder/_components/toolbar.tsx:130
msgid "Center Artboard"
msgstr "Papan Seni Tengah"

#: apps/client/src/pages/auth/reset-password/page.tsx:99
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:115
msgid "Change Password"
msgstr "Tukar Kata Laluan"

#: apps/client/src/components/ai-actions.tsx:97
msgid "Change Tone"
msgstr "Tukar Tone"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:186
msgid "Changed your mind about the name? Give it a new one."
msgstr "Tukar fikiran tentang nama itu? Berikan yang baru."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:70
msgid "Check your email for the confirmation link to update your email address."
msgstr "Periksa emel anda untuk pautan pengesahan bagi mengemaskini alamat emel anda."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:144
msgid "Circle"
msgstr "Bulatan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:249
msgid "Close"
msgstr "Tutup"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:201
msgid "Code"
msgstr "Kod"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:52
msgid "Code must be exactly 6 digits long."
msgstr "Kod mesti tepat 6 digit panjang."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:136
msgid "Columns"
msgstr "Kolum"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:39
msgid "Company"
msgstr "Syarikat"

#: apps/client/src/components/ai-actions.tsx:115
#: apps/client/src/components/ai-actions.tsx:118
msgid "Confident"
msgstr "Keyakinan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:234
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:246
msgid "Continue"
msgstr "Teruskan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:96
msgid "Copy"
msgstr "Salin"

#: apps/client/src/pages/builder/_components/toolbar.tsx:164
msgid "Copy Link to Resume"
msgstr "Salin Pautan ke Resume"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:78
msgid "Copy to Clipboard"
msgstr "Salin ke Papan Keratan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:179
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:244
msgid "Create"
msgstr "Cipta"

#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/register/page.tsx:69
msgid "Create a new account"
msgstr "Cipta akaun baru"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:163
msgid "Create a new item"
msgstr "Cipta item baru"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:178
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:18
msgid "Create a new resume"
msgstr "Mencipta resume baru"

#: apps/client/src/pages/auth/login/page.tsx:65
msgctxt "This is a link to create a new account"
msgid "Create one now"
msgstr "Cipta satu sekarang"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:259
msgid "Create Sample Resume"
msgstr "Buat Resume Sampel"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:82
msgid "Current Password"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/index.tsx:93
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:27
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:28
msgid "Custom CSS"
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:62
msgid "Custom resume sections"
msgstr "Seksyen resume khas"

#: apps/client/src/stores/resume.ts:47
msgid "Custom Section"
msgstr "Bahagian Kustom"

#: apps/client/src/pages/home/<USER>/features/index.tsx:60
msgid "Customisable colour palettes"
msgstr "Palet warna yang boleh disesuaikan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:61
msgid "Customisable layouts"
msgstr "Reka bentuk yang boleh disesuaikan"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:62
msgid "Danger Zone"
msgstr "Zon Bahaya"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:87
msgid "Dark"
msgstr "Gelap"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:67
msgid "Date"
msgstr "Tarikh"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:110
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:72
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:67
msgid "Date or Date Range"
msgstr "Tarikh atau Julat Tarikh"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:137
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:158
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:121
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:127
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:172
msgid "Delete"
msgstr "Padam"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:79
#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Delete Account"
msgstr "Padam Akaun"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:50
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:53
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:63
msgid "Description"
msgstr "Penerangan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:58
msgid "Design single/multi page resumes"
msgstr "Mereka bentuk resume satu/multi muka surat"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:139
msgid "Disable"
msgstr "Matikan"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:154
msgid "Disable 2FA"
msgstr "Matikan 2FA"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:302
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:220
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:134
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:118
msgid "Discard"
msgstr "Buang"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:105
msgid "Documentation"
msgstr "Dokumentasi"

#: apps/client/src/pages/auth/login/page.tsx:62
msgid "Don't have an account?"
msgstr "Tidak mempunyai akaun?"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:83
msgid "Don't know where to begin? Hit the docs!"
msgstr "Tidak tahu di mana hendak bermula? Ketuk dokumen!"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:107
msgid "Don't see your language? <0>Help translate the app.</0>"
msgstr "Jangan lihat bahasa anda? <0>Bantu terjemahkan aplikasi ini.</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:40
msgid "Donate to Reactive Resume"
msgstr "Derma kepada Reaktif Resume"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:56
msgid "Download a JSON snapshot of your resume. This file can be used to import your resume in the future, or can even be shared with others to collaborate."
msgstr "Muat turun 'snapshot' JSON resume anda. Fail ini boleh digunakan untuk mengimport resume anda di masa depan, atau boleh dikongsi dengan orang lain untuk bekerjasama."

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:74
msgid "Download a PDF of your resume. This file can be used to print your resume, send it to recruiters, or upload on job portals."
msgstr "Muat turunkan PDF resume anda. Fail ini boleh digunakan untuk mencetak resume anda, menghantarkannya kepada perekrut, atau memuat naik ke portal pekerjaan."

#: apps/client/src/pages/builder/_components/toolbar.tsx:176
msgid "Download PDF"
msgstr "Muat turun PDF"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:58
msgid "Downloads"
msgstr "Muat turun"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:181
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:246
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:95
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:156
msgid "Duplicate"
msgstr "Duplikat"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:165
msgid "Duplicate an existing item"
msgstr "Duplikat item yang sedia ada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:180
msgid "Duplicate an existing resume"
msgstr "Duplikat resume sedia ada"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:92
msgid "Edit"
msgstr "Edit"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:169
msgid "Effects"
msgstr "Kesan"

#: apps/client/src/pages/auth/forgot-password/page.tsx:82
#: apps/client/src/pages/auth/login/page.tsx:90
#: apps/client/src/pages/auth/register/page.tsx:141
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:54
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:181
msgid "Email"
msgstr "E-mel"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:163
msgid "Enable 2FA"
msgstr "Aktifkan 2FA"

#: apps/client/src/pages/auth/reset-password/page.tsx:67
msgid "Enter a new password below, and make sure it's secure."
msgstr "Masukkan kata laluan baru di bawah, dan pastikan ia selamat."

#: apps/client/src/pages/auth/backup-otp/page.tsx:59
msgid "Enter one of the 10 backup codes you saved when you enabled two-factor authentication."
msgstr "Masukkan salah satu daripada kod sandaran 10 yang anda simpan apabila anda mengaktifkan pengesahan dua faktor."

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:63
msgid "Enter Phosphor Icon"
msgstr "Masukkan Ikon Fosfor"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:170
msgid "Enter the 6-digit code from your authenticator app to verify that 2FA has been setup correctly."
msgstr "Masukkan kod 6 digit dari aplikasi pengesah anda untuk mengesahkan bahawa 2FA telah disediakan dengan betul."

#: apps/client/src/pages/auth/verify-otp/page.tsx:60
msgid "Enter the one-time password provided by your authenticator app below."
msgstr "Masukkan kata laluan sekali pakai yang diberikan oleh aplikasi pengesahan anda di bawah."

#: apps/client/src/pages/auth/forgot-password/page.tsx:70
msgid "Enter your email address and we will send you a link to reset your password if the account exists."
msgstr "Masukkan alamat emel anda dan kami akan menghantar pautan untuk menetap semula kata laluan anda jika akaun wujud."

#: apps/client/src/pages/public/error.tsx:46
msgid "Error {statusCode}"
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:283
msgid "Errors"
msgstr "Ralat"

#: apps/client/src/pages/home/<USER>/support/index.tsx:78
msgid "Even if you're not in a position to contribute financially, you can still make a difference by giving the GitHub repository a star, spreading the word to your friends, or dropping a quick message to let me know how Reactive Resume has helped you. Your feedback and support are always welcome and much appreciated!"
msgstr "Walaupun anda tidak mampu memberikan sumbangan kewangan, anda masih boleh membuat perbezaan dengan memberikan bintang kepada repositori GitHub, menyebarkan berita kepada rakan anda, atau meninggalkan mesej ringkas untuk memberitahu saya bagaimana Reaktif Resume telah membantu anda. Maklum balas dan sokongan anda sentiasa dialu-alukan dan dihargai!"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:12
msgid "Explore the templates available in Reactive Resume and view the resumes crafted with them. They could also serve as examples to help guide the creation of your next resume."
msgstr "Terokai templat yang tersedia dalam Reaktif Resume dan lihat resume yang dibuat dengan mereka. Mereka juga boleh berfungsi sebagai contoh untuk membimbing penciptaan resume anda seterusnya."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:121
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:39
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:40
msgid "Export"
msgstr "Eksport"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:255
msgid "File"
msgstr "Fail"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:221
msgid "Filetype"
msgstr "JenisFail"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:38
msgid "Finally,"
msgstr "Akhirnya,"

#: apps/client/src/components/ai-actions.tsx:90
msgid "Fix Spelling & Grammar"
msgstr "Betulkan Ejaan & Tatabahasa"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:107
msgid "Font Family"
msgstr "Keluarga fon"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:148
msgid "Font Size"
msgstr "Saiz Fon"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:122
msgid "Font Subset"
msgstr "Subset Fon"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:134
msgid "Font Variants"
msgstr "Varian Fon"

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:35
msgid "For example, information regarding which companies you sent this resume to or the links to the job descriptions can be noted down here."
msgstr "Sebagai contoh, maklumat mengenai syarikat-syarikat yang anda hantar resume ini atau pautan-pautan ke huraian pekerjaan boleh dicatatkan di sini."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:182
msgid "Forget"
msgstr "Lupakan"

#: apps/client/src/pages/auth/login/page.tsx:131
msgid "Forgot Password?"
msgstr "Lupa Kata Laluan?"

#: apps/client/src/pages/auth/forgot-password/page.tsx:68
msgid "Forgot your password?"
msgstr "Lupa kata laluan anda?"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:32
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:40
msgid "Format"
msgstr "Format"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:49
msgid "Found a bug, or have an idea for a new feature?"
msgstr "Jumpa bug, atau ada idea untuk ciri baru?"

#: apps/client/src/pages/home/<USER>/features/index.tsx:46
msgid "Free, forever"
msgstr "Percuma, selamanya"

#: apps/client/src/components/ai-actions.tsx:121
#: apps/client/src/components/ai-actions.tsx:124
msgid "Friendly"
msgstr "Mesra"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:31
msgid "Full Name"
msgstr "Nama penuh"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:202
msgid "Generate a random title for your resume"
msgstr "Hasilkan tajuk rawak untuk resume anda"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:32
msgid "Get Started"
msgstr "Mula"

#: apps/client/src/pages/auth/_components/social-auth.tsx:18
msgid "GitHub"
msgstr "GitHub"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:12
msgid "GitHub Stars"
msgstr "Bintang GitHub"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:187
msgid "Give your old resume a new name."
msgstr "Beri nama baru kepada resume lama anda."

#: apps/client/src/pages/auth/verify-email/page.tsx:67
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:18
msgid "Go to Dashboard"
msgstr "Pergi ke Dashboard"

#: apps/client/src/pages/public/error.tsx:55
msgid "Go to home"
msgstr ""

#: apps/client/src/pages/auth/_components/social-auth.tsx:31
msgid "Google"
msgstr "Google"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:202
msgid "Grayscale"
msgstr "Skala kelabu"

#: apps/client/src/pages/dashboard/resumes/page.tsx:43
msgid "Grid"
msgstr "Grid"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:43
msgid "Headline"
msgstr "Tajuk Utama"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:107
msgid "Here, you can update your account information such as your profile picture, name and username."
msgstr "Di sini, anda boleh mengemaskini maklumat akaun anda seperti gambar profil, nama dan nama pengguna."

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:68
msgid "Here, you can update your profile to customize and personalize your experience."
msgstr "Di sini, anda boleh mengemaskini profil anda untuk menyesuaikan dan memperibadikan pengalaman anda."

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:80
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:94
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:180
msgid "Hidden"
msgstr "Tersembunyi"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Hide"
msgstr "Sembunyi"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:192
msgid "Hide Icons"
msgstr "Sembunyikan Icon"

#: apps/client/src/pages/auth/login/page.tsx:115
#: apps/client/src/pages/auth/register/page.tsx:168
#: apps/client/src/pages/auth/reset-password/page.tsx:88
msgid "Hold <0>Ctrl</0> to display your password temporarily."
msgstr "Tekan <0>Ctrl</0> untuk memaparkan kata laluan anda secara sementara."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:93
msgid "Horizontal"
msgstr "Horizontal"

#: apps/client/src/pages/home/<USER>/features/index.tsx:67
msgid "Host your resume publicly"
msgstr "Menyimpan resume anda secara awam"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:70
msgid "I always love to hear from the users of Reactive Resume with feedback or support. Here are some of the messages I've received. If you have any feedback, feel free to drop me an email at <0>{email}</0>."
msgstr "Saya sentiasa sukakan mendengar dari pengguna-pengguna Reactive Resume dengan maklum balas atau sokongan. Berikut adalah beberapa mesej yang saya terima. Jika anda mempunyai sebarang maklum balas, jangan ragu untuk menghantar emel kepada saya di <0>{email}</0>."

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:83
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:53
msgid "Icon"
msgstr "Ikon"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:47
msgid "If this app has helped you with your job hunt, let me know by reaching out through <0>this contact form</0>."
msgstr "Jika aplikasi ini telah membantu anda dalam mencari pekerjaan, beritahu saya dengan menghubungi melalui <0>borang hubungan ini</0>."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:128
msgid "If you disable two-factor authentication, you will no longer be required to enter a verification code when logging in."
msgstr "Jika anda mematikan pengesahan dua faktor, anda tidak perlu lagi memasukkan kod pengesahan semasa log masuk."

#: apps/client/src/pages/home/<USER>/support/index.tsx:59
msgid "If you're multilingual, we'd love your help in bringing the app to more languages and communities. Don't worry if you don't see your language on the list - just give me a shout-out on GitHub, and I'll make sure to include it. Ready to get started? Jump into translation over at Crowdin by clicking the link below."
msgstr "Jika anda berkebolehan berbicara dalam beberapa bahasa, kami mengalu-alukan bantuan anda dalam membawa aplikasi ini ke lebih banyak bahasa dan komuniti. Jangan risau jika anda tidak melihat bahasa anda dalam senarai - beritahu saya di GitHub, dan saya akan pastikan untuk menyertainya. Sedia untuk memulakan? Mula terjemahan di Crowdin dengan mengklik pautan di bawah."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:309
msgid "Import"
msgstr "Import"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:208
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:17
msgid "Import an existing resume"
msgstr "Import resume sedia ada"

#: apps/client/src/components/ai-actions.tsx:85
msgid "Improve Writing"
msgstr "Meningkatkan Menulis"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:188
msgid "In case you are unable to scan this QR Code, you can also copy-paste this link into your authenticator app."
msgstr "Sekiranya anda tidak boleh mengimbas Kod QR ini, anda juga boleh menyalin-dan-tampal pautan ini ke dalam aplikasi pengesah anda."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:67
msgid "In this section, you can change your password and enable/disable two-factor authentication."
msgstr "Di bahagian ini, anda boleh menukar kata laluan anda dan mengaktifkan/menonaktifkan pengesahan dua faktor."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:64
msgid "In this section, you can delete your account and all the data associated to your user, but please keep in mind that <0>this action is irreversible</0>."
msgstr "Dalam bahagian ini, anda boleh memadam akaun anda dan semua data yang berkaitan dengan pengguna anda, tetapi sila ambil perhatian bahawa <0>tindakan ini tidak dapat dikembalikan</0>."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:135
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:116
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:117
msgid "Information"
msgstr "Maklumat"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:39
msgid "Institution"
msgstr "Institusi"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:53
msgid "Issuer"
msgstr "Penerbit"

#: apps/client/src/services/errors/translate-error.ts:7
msgid "It doesn't look like a user exists with the credentials you provided."
msgstr "Nampaknya tiada pengguna wujud dengan kelayakan yang anda berikan."

#: apps/client/src/services/errors/translate-error.ts:37
msgid "It looks like the backup code you provided is invalid or used. Please try again."
msgstr "Nampaknya kod sandaran yang anda berikan tidak sah atau telah digunakan. Sila cuba lagi."

#: apps/client/src/services/errors/translate-error.ts:19
msgid "It looks like the reset token you provided is invalid. Please try restarting the password reset process again."
msgstr "Nampaknya token reset yang anda berikan tidak sah. Sila cuba memulakan semula proses set semula kata laluan lagi."

#: apps/client/src/services/errors/translate-error.ts:46
msgid "It looks like the resume you're looking for doesn't exist."
msgstr "Nampaknya resume yang anda cari tidak wujud."

#: apps/client/src/services/errors/translate-error.ts:34
msgid "It looks like the two-factor authentication code you provided is invalid. Please try again."
msgstr "Nampaknya kod pengesahan dua faktor yang anda berikan tidak sah. Sila cuba lagi."

#: apps/client/src/services/errors/translate-error.ts:22
msgid "It looks like the verification token you provided is invalid. Please try restarting the verification process again."
msgstr "Nampaknya token pengesahan yang anda berikan tidak sah. Sila cuba untuk memulakan semula proses pengesahan."

#: apps/client/src/services/errors/translate-error.ts:25
msgid "It looks like your email address has already been verified."
msgstr "Nampaknya alamat emel anda telah disahkan."

#: apps/client/src/pages/auth/register/page.tsx:101
msgctxt "Localized version of a placeholder name. For example, Max Mustermann in German or Jan Kowalski in Polish."
msgid "John Doe"
msgstr "John Doe"

#: apps/client/src/pages/auth/register/page.tsx:123
msgctxt "Localized version of a placeholder username. For example, max.mustermann in German or jan.kowalski in Polish."
msgid "john.doe"
msgstr "john.doe"

#: apps/client/src/pages/auth/register/page.tsx:145
msgctxt "Localized version of a placeholder email. For example, <EMAIL> in <NAME_EMAIL> in Polish."
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:54
msgid "JSON"
msgstr "JSON"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:63
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:109
msgid "Keywords"
msgstr "Kata Kunci"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:42
#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:52
msgid "Label"
msgstr "Label"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:101
msgid "Language"
msgstr "Bahasa"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:83
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:139
msgid "Last updated {lastUpdated}"
msgstr "Terakhir dikemaskini {lastUpdated}"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:72
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:197
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:198
msgid "Layout"
msgstr "Tataletak"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:38
msgid "Learn more"
msgstr "Ketahui lebih lanjut"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:44
msgid "Letter"
msgstr "Surat"

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:64
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:77
msgid "Level"
msgstr "Tahap"

#: apps/client/src/components/copyright.tsx:16
msgid "Licensed under <0>MIT</0>"
msgstr "Dilisensikan di bawah <0>MIT</0>"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:86
msgid "Light"
msgstr "Cerah"

#: apps/client/src/pages/home/<USER>/features/index.tsx:69
msgid "Light or dark theme"
msgstr "Tema terang atau gelap"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:165
msgid "Line Height"
msgstr "Tinggi Baris"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:22
msgid "LinkedIn, JSON Resume, etc."
msgstr "LinkedIn, JSON Resume, etc."

#: apps/client/src/pages/dashboard/resumes/page.tsx:47
msgid "List"
msgstr "Senarai"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:86
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:81
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:93
msgid "Location"
msgstr "Lokasi"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:51
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:166
msgid "Lock"
msgstr "Kunci"

#: apps/client/src/pages/home/<USER>/features/index.tsx:64
msgid "Lock a resume to prevent editing"
msgstr "Kunci reka bentuk untuk menghalang pengeditan"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:43
msgid "Locking a resume will prevent any further changes to it. This is useful when you have already shared your resume with someone and you don't want to accidentally make any changes to it."
msgstr "Mengunci resume akan menghalang sebarang perubahan lanjut kepadanya. Ini berguna apabila anda sudah berkongsi resume anda dengan seseorang dan anda tidak mahu secara tidak sengaja membuat sebarang perubahan kepadanya."

#: apps/client/src/components/user-options.tsx:38
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:23
msgid "Logout"
msgstr "Log keluar"

#: apps/client/src/pages/auth/verify-otp/page.tsx:64
msgid "Lost your device?"
msgstr "Hilang peranti anda?"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:247
msgid "Main"
msgstr "Utama"

#: apps/client/src/pages/home/<USER>/features/index.tsx:59
msgid "Manage multiple resumes"
msgstr "Urus banyak resume"

#. The month and year should be uniform across all languages.
#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:71
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:69
msgid "March 2023"
msgstr "Mac 2023"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:112
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:74
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:103
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:69
msgid "March 2023 - Present"
msgstr "Mac 2023 - Sekarang"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:50
msgid "Margin"
msgstr "Margin"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:158
msgid "Max Tokens"
msgstr "Tanda-tanda terbesar"

#: apps/client/src/pages/home/<USER>/features/index.tsx:48
msgid "MIT License"
msgstr "Lesen MIT"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:145
msgid "Model"
msgstr "Modal"

#: apps/client/src/pages/auth/register/page.tsx:98
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:59
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:48
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:36
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:49
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:88
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:153
msgid "Name"
msgstr "Nama"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:39
msgctxt "Name of the Certification"
msgid "Name"
msgstr "Nama"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:40
msgid "Network"
msgstr "Rangkaian"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:96
msgid "New Password"
msgstr "Kata Laluan Baru"

#: apps/client/src/components/locale-combobox.tsx:45
msgid "No results found"
msgstr "Tiada keputusan dijumpai"

#: apps/client/src/pages/home/<USER>/features/index.tsx:49
msgid "No user tracking or advertising"
msgstr "Tiada penjejakan pengguna atau pengiklanan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:133
msgid "Note: This will make your account less secure."
msgstr "Nota: Ini akan menjadikan akaun anda kurang selamat."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:128
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:16
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:17
msgid "Notes"
msgstr "Nota"

#: apps/client/src/pages/auth/verify-otp/page.tsx:82
msgid "One-Time Password"
msgstr "Kata Laluan Sekali"

#: apps/client/src/components/ai-actions.tsx:56
#: apps/client/src/libs/axios.ts:30
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:188
#: apps/client/src/services/resume/print.tsx:26
msgid "Oops, the server returned an error."
msgstr "Ups, pelayan mengembalikan ralat."

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:97
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:77
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:148
msgid "Open"
msgstr "Buka"

#: apps/client/src/pages/home/<USER>/features/index.tsx:47
msgid "Open Source"
msgstr "Sumber Terbuka"

#: apps/client/src/services/openai/change-tone.ts:35
#: apps/client/src/services/openai/fix-grammar.ts:33
#: apps/client/src/services/openai/improve-writing.ts:33
msgid "OpenAI did not return any choices for your text."
msgstr "OpenAI tidak mengembalikan pilihan apa pun untuk teks anda."

#: apps/client/src/pages/home/<USER>/features/index.tsx:52
msgid "OpenAI Integration"
msgstr "Integrasi OpenAI"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:119
msgid "OpenAI/Ollama API Key"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:79
msgid "OpenAI/Ollama Integration"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:67
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:182
msgid "Options"
msgstr "Pilihan"

#: apps/client/src/pages/auth/layout.tsx:47
msgctxt "The user can either login with email/password, or continue with GitHub or Google."
msgid "or continue with"
msgstr "atau teruskan dengan"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:39
msgid "Organization"
msgstr "Organisasi"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:100
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:25
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:26
msgid "Page"
msgstr "Halaman"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:228
msgid "Page {pageNumber}"
msgstr ""

#: apps/client/src/pages/auth/login/page.tsx:110
#: apps/client/src/pages/auth/register/page.tsx:163
#: apps/client/src/pages/auth/reset-password/page.tsx:83
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:73
msgid "Password"
msgstr "Kata Laluan"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:72
msgid "PDF"
msgstr "PDF"

#: apps/client/src/pages/home/<USER>/features/index.tsx:63
msgid "Personal notes for each resume"
msgstr "Nota peribadi untuk setiap resume"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:81
msgid "Phone"
msgstr "Telefon"

#: apps/client/src/pages/auth/layout.tsx:76
msgid "Photograph by Patrick Tomasso"
msgstr "Gambar oleh Patrick Tomasso"

#: apps/client/src/pages/home/<USER>/features/index.tsx:66
msgid "Pick any font from Google Fonts"
msgstr "Pilih mana-mana fon dari Google Fonts"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/section.tsx:69
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:121
msgid "Picture"
msgstr "Gambar"

#: apps/client/src/pages/auth/verify-email/page.tsx:59
msgid "Please note that this step is completely optional."
msgstr "Sila ambil perhatian bahawa langkah ini adalah pilihan sepenuhnya."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:225
msgid "Please select a file type"
msgstr "Sila pilih jenis fail"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:228
msgid "Please store your backup codes in a secure location. You can use one of these one-time use codes to login in case you lose access to your authenticator app."
msgstr "Sila simpan kod sandaran anda di lokasi yang selamat. Anda boleh menggunakan salah satu kod gunaan sekali untuk log masuk jika anda kehilangan akses kepada aplikasi pengesah anda."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:99
msgid "Portrait"
msgstr "Potret"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:54
msgctxt "Position held at a company, for example, Software Engineer"
msgid "Position"
msgstr "Kedudukan"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:53
msgid "Position"
msgstr "Kedudukan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:96
msgid "Powered by"
msgstr "Dikuasakan oleh"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:94
msgid "Powered by <0>Simple Icons</0>"
msgstr "Dikuasakan oleh <0>Simple Icons</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:43
msgid "Primary Color"
msgstr "Warna Utama"

#: apps/client/src/pages/home/<USER>/footer.tsx:50
msgid "Privacy Policy"
msgstr "Dasar Privasi"

#: apps/client/src/components/ai-actions.tsx:109
#: apps/client/src/components/ai-actions.tsx:112
msgid "Professional"
msgstr "Profesional"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:66
msgid "Profile"
msgstr "Profil"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:55
msgid "Public"
msgstr "Awam"

#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:53
msgid "Publisher"
msgstr "Penerbit"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:69
msgid "Raise an issue"
msgstr "Timbulkan isu"

#: apps/client/src/components/copyright.tsx:35
#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/builder/page.tsx:60
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/home/<USER>/footer.tsx:18
#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/public/page.tsx:74
#: apps/client/src/pages/public/page.tsx:95
msgid "Reactive Resume"
msgstr "Reactive Resume"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:39
msgid "Reactive Resume has helped people land jobs at these great companies:"
msgstr "Reactive Resume telah membantu orang mendapatkan pekerjaan di syarikat-syarikat hebat ini:"

#: apps/client/src/pages/home/<USER>/support/index.tsx:12
msgid "Reactive Resume is a free and open-source project crafted mostly by me, and your support would be greatly appreciated. If you're inclined to contribute, and only if you can afford to, consider making a donation through any of the listed platforms. Additionally, donations to Reactive Resume through Open Collective are tax-exempt, as the project is fiscally hosted by Open Collective Europe."
msgstr "Reactive Resume adalah projek sumber terbuka dan percuma yang kebanyakannya dihasilkan oleh saya, dan sokongan anda akan amat dihargai. Jika anda ingin menyumbang, dan hanya jika anda mampu, pertimbangkan untuk membuat sumbangan melalui mana-mana platform yang disenaraikan. Selain itu, sumbangan kepada Reactive Resume melalui Kolektif Terbuka adalah dikecualikan cukai, kerana projek ini dihoskan secara kewangan oleh Kolektif Terbuka Eropah."

#: apps/client/src/pages/home/<USER>/features/index.tsx:107
msgid "Reactive Resume is a passion project of over 3 years of hard work, and with that comes a number of re-iterated ideas and features that have been built to (near) perfection."
msgstr "Reactive Resume adalah projek minat selama lebih daripada 3 tahun kerja keras, dan dengan itu datang beberapa idea dan ciri-ciri yang telah dibangunkan sehingga (hampir) ke sempurnaan."

#: apps/client/src/pages/home/<USER>/contributors/index.tsx:22
msgid "Reactive Resume thrives thanks to its vibrant community. This project owes its progress to numerous individuals who've dedicated their time and skills. Below, we celebrate the coders who've enhanced its features on GitHub and the linguists whose translations on Crowdin have made it accessible to a broader audience."
msgstr "Reactive Resume berkembang pesat berkat komuniti yang aktif. Projek ini berhutang kepada ramai individu yang telah mendedikasikan masa dan kemahiran mereka. Di bawah ini, kami merayakan coder yang telah meningkatkan ciri-ciri projek ini di GitHub dan penterjemah yang telah menterjemahkan di Crowdin untuk membolehkannya diakses oleh audiens yang lebih luas."

#: apps/client/src/pages/builder/_components/toolbar.tsx:89
msgid "Redo"
msgstr "Buat semula"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:100
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:157
msgid "Remove"
msgstr "Keluarkan"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:231
msgid "Remove Page"
msgstr "Buang Halaman"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:111
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:101
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:86
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:152
msgid "Rename"
msgstr "Namakan"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:199
msgid "Resend email confirmation link"
msgstr "Kirim semula pautan pengesahan emel"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:152
msgid "Reset"
msgstr "Tetapkan Semula"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:201
msgid "Reset Layout"
msgstr "Tetapkan Susunan Semula"

#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/reset-password/page.tsx:65
msgid "Reset your password"
msgstr "Tetapkan semula kata laluan anda"

#: apps/client/src/pages/builder/_components/toolbar.tsx:124
msgid "Reset Zoom"
msgstr "Tetap Semula Zoom"

#: apps/client/src/pages/dashboard/_components/sidebar.tsx:86
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/resumes/page.tsx:37
msgid "Resumes"
msgstr "Resume"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:14
msgid "Resumes Generated"
msgstr "Resumen Dihasilkan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:105
msgid "Rich in features, not in pricing."
msgstr "Kaya dengan ciri-ciri, bukan harga."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:138
msgid "Rounded"
msgstr "Bulat"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:180
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:245
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:217
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:131
msgid "Save Changes"
msgstr "Simpan Perubahan"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Save Locally"
msgstr "Simpan di dalam computer"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Saved"
msgstr "Disimpan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:168
msgid "Scan the QR code below with your authenticator app to setup 2FA on your account."
msgstr "Imbas kod QR di bawah dengan aplikasi pengesah anda untuk setup 2FA pada akaun anda."

#. Score or honors for the degree, for example, CGPA or magna cum laude
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:92
msgid "Score"
msgstr "Markah"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Pan"
msgstr ""

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Zoom"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:111
msgid "Search for a font family"
msgstr "Cari keluarga fon"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:126
msgid "Search for a font subset"
msgstr "Cari subset fon"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:139
msgid "Search for a font variant"
msgstr "Cari varian font"

#: apps/client/src/components/locale-combobox.tsx:41
msgid "Search for a language"
msgstr "Cari bahasa"

#: apps/client/src/pages/home/<USER>/features/index.tsx:56
msgid "Secure with two-factor authentication"
msgstr "Selamatkan dengan pengesahan dua faktor"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:65
msgid "Security"
msgstr "Keselamatan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:50
msgid "Self-host with Docker"
msgstr "Taja sendiri dengan Docker"

#: apps/client/src/pages/auth/forgot-password/page.tsx:104
msgid "Send Email"
msgstr "Hantar Email"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:74
msgid "Send me a message"
msgstr "Hantar saya mesej"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:97
msgid "Separate Links"
msgstr "Pautan Berasingan"

#: apps/client/src/components/user-options.tsx:32
#: apps/client/src/pages/dashboard/_components/sidebar.tsx:92
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/dashboard/settings/page.tsx:26
msgid "Settings"
msgstr "Tetapan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:159
msgid "Setup two-factor authentication on your account"
msgstr "Tetapkan pengesah dua faktor pada akaun anda"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:107
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:38
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:39
msgid "Sharing"
msgstr " Berkongsi"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Show"
msgstr "Paparkan"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:78
msgid "Show Break Line"
msgstr "Tunjukkan Garis Pemisah"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:91
msgid "Show Page Numbers"
msgstr "Paparkan Nombor Halaman"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:248
msgid "Sidebar"
msgstr "Panel Sisi"

#: apps/client/src/pages/auth/backup-otp/page.tsx:103
#: apps/client/src/pages/auth/login/page.tsx:127
#: apps/client/src/pages/auth/verify-otp/page.tsx:92
msgid "Sign in"
msgstr "Log masuk"

#: apps/client/src/pages/auth/register/page.tsx:74
msgid "Sign in now"
msgstr "Log masuk sekarang"

#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/login/page.tsx:60
msgid "Sign in to your account"
msgstr "Log masuk ke akaun anda"

#: apps/client/src/pages/home/<USER>/features/index.tsx:55
msgid "Sign in with Email"
msgstr "Log masuk dengan E-mel"

#: apps/client/src/pages/home/<USER>/features/index.tsx:53
msgid "Sign in with GitHub"
msgstr "Log masuk dengan GitHub"

#: apps/client/src/pages/home/<USER>/features/index.tsx:54
msgid "Sign in with Google"
msgstr "Daftar masuk dengan Google"

#: apps/client/src/pages/auth/register/page.tsx:179
msgid "Sign up"
msgstr "Daftar"

#: apps/client/src/pages/auth/login/page.tsx:74
msgid "Signing in via email is currently disabled by the administrator."
msgstr "Pendaftaran melalui emel kini dinyahaktifkan oleh pentadbir."

#: apps/client/src/pages/auth/register/page.tsx:82
msgid "Signups are currently disabled by the administrator."
msgstr "Pendaftaran kini dilumpuhkan oleh pentadbir."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:65
msgid "Size (in px)"
msgstr "Saiz (dalam px)"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:228
msgid "Slug"
msgstr "Slug"

#: apps/client/src/services/errors/translate-error.ts:55
msgid "Something went wrong while grabbing a preview your resume. Please try again later or raise an issue on GitHub."
msgstr "Ada masalah semasa mengambil pratonton resume anda. Sila cuba lagi kemudian atau bangkitkan isu di GitHub."

#: apps/client/src/services/errors/translate-error.ts:52
msgid "Something went wrong while printing your resume. Please try again later or raise an issue on GitHub."
msgstr "Berlaku ralat semasa mencetak resume anda. Sila cuba sekali lagi kemudian atau timbulkan isu di GitHub."

#: apps/client/src/services/errors/translate-error.ts:58
msgid "Something went wrong while processing your request. Please try again later or raise an issue on GitHub."
msgstr "Ada yang tidak kena semasa memproses permintaan anda. Sila cuba lagi kemudian atau timbulkan isu di GitHub."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:87
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:132
msgid "Square"
msgstr "Petak"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:23
msgid "Start building from scratch"
msgstr "Mula membina dari awal"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:185
msgid "Start building your resume by giving it a name."
msgstr "Mula bina resume anda dengan memberinya nama."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:114
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:22
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:23
msgid "Statistics"
msgstr "Statistik"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:38
msgid "Statistics are available only for public resumes."
msgstr "Statistik hanya tersedia untuk resume awam."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:162
msgid "Store your backup codes securely"
msgstr "Simpan kod sandaran anda dengan selamat"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:138
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:114
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:109
msgid "Summary"
msgstr "Ringkasan"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:18
msgid "Support the app by donating what you can!"
msgstr "Menyokong aplikasi dengan menderma sebanyak yang anda mampu!"

#: apps/client/src/pages/home/<USER>/support/index.tsx:9
msgid "Supporting Reactive Resume"
msgstr "Sokongan Resume Reaktif"

#: apps/client/src/pages/home/<USER>/features/index.tsx:65
msgid "Supports A4/Letter page formats"
msgstr "Mendukung format halaman A4/Surat"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:85
msgid "System"
msgstr "Sistem"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:65
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:18
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:19
msgid "Template"
msgstr "Templat"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:9
msgid "Templates"
msgstr "Templat"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:68
msgid "Testimonials"
msgstr "Testimoni"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:103
msgid "Text Color"
msgstr "Warna Teks"

#: apps/client/src/pages/public/error.tsx:17
msgid "The page you're looking for doesn't exist."
msgstr ""

#: apps/client/src/pages/public/error.tsx:29
msgid "The request was invalid."
msgstr ""

#: apps/client/src/services/errors/translate-error.ts:49
msgid "The resume you want to update is locked, please unlock if you wish to make any changes to it."
msgstr "Resume yang anda ingin kemaskini telah dikunci, sila buka kuncinya jika anda ingin membuat sebarang perubahan padanya."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:86
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:19
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:20
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:79
msgid "Theme"
msgstr "Tema"

#: apps/client/src/services/errors/translate-error.ts:40
msgid "There was an error connecting to the browser. Please make sure 'chrome' is running and reachable."
msgstr "Terdapat ralat semasa menyambung ke pelayar. Sila pastikan 'chrome' sedang berfungsi dan boleh dihubungi."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:130
msgid "This action can be reverted by clicking on the undo button in the floating toolbar."
msgstr "Tindakan ini boleh dikembalikan dengan mengklik butang batal di toolbar terapung."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:151
msgid "This action cannot be undone. This will permanently delete your resume and cannot be recovered."
msgstr "Tindakan ini tidak boleh dibatalkan. Ini akan memadamkan secara kekal resume anda dan tidak dapat dipulihkan."

#: apps/client/src/services/errors/translate-error.ts:16
msgid "This email address is associated with an OAuth account. Please sign in with your OAuth provider."
msgstr "Alamat emel ini berkaitan dengan akaun OAuth. Sila log masuk dengan pembekal OAuth anda."

#: apps/client/src/pages/builder/_components/header.tsx:57
msgid "This resume is locked, please unlock to make further changes."
msgstr "Resume ini dikunci, sila buka kuncinya untuk membuat perubahan lanjut."

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:23
msgid "This section is reserved for your personal notes specific to this resume. The content here remains private and is not shared with anyone else."
msgstr "Seksyen ini adalah untuk catatan peribadi anda yang khusus untuk resume ini. Kandungan di sini kekal peribadi dan tidak dikongsi dengan sesiapa pun."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:216
msgid "Tip: You can name the resume referring to the position you are applying for."
msgstr "Cadangan: Anda boleh memberi nama resume merujuk kepada jawatan yang anda memohon."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:39
msgctxt "Name of the Award"
msgid "Title"
msgstr "Tajuk"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:196
msgid "Title"
msgstr "Tajuk"

#: apps/client/src/pages/builder/_components/toolbar.tsx:138
msgid "Toggle Page Break Line"
msgstr "Baris Putus Halaman Toggle"

#: apps/client/src/pages/builder/_components/toolbar.tsx:150
msgid "Toggle Page Numbers"
msgstr "Papar Nombor Halaman"

#: apps/client/src/pages/home/<USER>/features/index.tsx:68
msgid "Track views and downloads"
msgstr "Jejak pandangan dan muat turun"

#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/auth/verify-otp/page.tsx:57
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:129
msgid "Two-Factor Authentication"
msgstr "Pengesahan Dua Faktor"

#: apps/client/src/services/errors/translate-error.ts:31
msgid "Two-factor authentication is already enabled for this account."
msgstr "Pengesahan dua faktor sudah diaktifkan untuk akaun ini."

#: apps/client/src/services/errors/translate-error.ts:28
msgid "Two-factor authentication is not enabled for this account."
msgstr "Pengesahan dua faktor tidak diaktifkan untuk akaun ini."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:84
msgid "Type <0>delete</0> to confirm deleting your account."
msgstr "Taip <0>padam</0> untuk mengesahkan pemadaman akaun anda."

#. For example, Bachelor's Degree or Master's Degree
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:54
msgid "Type of Study"
msgstr "Jenis Kajian"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:79
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:76
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:77
msgid "Typography"
msgstr "Tipografi"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:203
msgid "Underline Links"
msgstr "Garis Bawah Pautan"

#: apps/client/src/pages/builder/_components/toolbar.tsx:76
msgid "Undo"
msgstr "Undurkan"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:52
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:110
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:161
msgid "Unlock"
msgstr "Buka Kunci"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:44
msgid "Unlocking a resume will allow you to make changes to it again."
msgstr "Membuka semula resume akan membolehkan anda membuat perubahan padanya sekali lagi."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Unverified"
msgstr "Belum disahkan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:164
msgid "Update an existing item"
msgstr "Kemas kini item yang sedia ada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:179
msgid "Update an existing resume"
msgstr "Kemas kini resume sedia ada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:212
msgid "Upload a file from one of the accepted sources to parse existing data and import it into Reactive Resume for easier editing."
msgstr "Muat naik fail dari salah satu sumber yang diterima untuk menganalisis data sedia ada dan import ke Reactive Resume untuk penyuntingan yang lebih mudah."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:73
msgid "URL"
msgstr "URL"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:61
msgid "URL must start with https://"
msgstr "URL mesti bermula dengan https://"

#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/backup-otp/page.tsx:57
msgid "Use your backup code"
msgstr "Guna kod sandaran anda"

#: apps/client/src/services/errors/translate-error.ts:13
msgid "User does not have an associated 'secrets' record. Please report this issue on GitHub."
msgstr "Pengguna tidak mempunyai rekod 'rahsia' yang berkaitan. Sila laporkan isu ini di GitHub."

#: apps/client/src/pages/auth/register/page.tsx:119
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:55
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:167
msgid "Username"
msgstr "Nama pengguna"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:13
msgid "Users Signed Up"
msgstr "Pengguna Telah Mendaftar"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:296
msgid "Validate"
msgstr "Sahkan"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:314
msgid "Validated"
msgstr "Disahkan"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:97
msgid "Value"
msgstr "Nilai"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Verified"
msgstr "Disahkan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:161
msgid "Verify that two-factor authentication has been setup correctly"
msgstr "Pastikan bahawa pengesahan dua faktor telah disediakan dengan betul"

#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-email/page.tsx:48
msgid "Verify your email address"
msgstr "Sahkan alamat emel anda"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:26
msgid "Version 4"
msgstr "Versi 4"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:51
msgid "Views"
msgstr "Pandangan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:87
msgid "Visible"
msgstr "Kelihatan"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:70
msgid "Visit <0>Phosphor Icons</0> for a list of available icons"
msgstr ""

#: apps/client/src/pages/auth/verify-email/page.tsx:61
msgid "We verify your email address only to ensure that we can send you a password reset link in case you forget your password."
msgstr "Kami mengesahkan alamat emel anda hanya untuk memastikan bahawa kami boleh menghantar pautan set semula kata laluan jika anda lupa kata laluan anda."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:124
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:100
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:95
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:69
msgid "Website"
msgstr "Laman web"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:32
msgid "What's new in the latest version"
msgstr "Apakah yang baru dalam versi terkini"

#: apps/client/src/pages/public/error.tsx:26
msgid "You are not authorized to access this page."
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:68
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:114
msgid "You can add multiple keywords by separating them with a comma or pressing enter."
msgstr "Anda boleh menambah beberapa kata kunci dengan memisahkan mereka dengan koma atau menekan enter."

#: apps/client/src/pages/auth/login/page.tsx:99
msgid "You can also enter your username."
msgstr "Anda juga boleh memasukkan nama pengguna anda."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:103
msgid "You can also integrate with Ollama simply by setting the API key to `sk-1234567890abcdef` and the Base URL to your Ollama URL, i.e. `http://localhost:11434/v1`. You can also pick and choose models and set the max tokens as per your preference."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:81
msgid "You can make use of the OpenAI API to help you generate content, or improve your writing while composing your resume."
msgstr "Anda boleh menggunakan API OpenAI untuk membantu anda menghasilkan kandungan, atau meningkatkan penulisan anda semasa menyusun resume anda."

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:40
msgid "You can track the number of views your resume has received, or how many people have downloaded the resume by enabling public sharing."
msgstr "Anda boleh mengesan jumlah paparan resume anda telah diterima, atau berapa ramai orang yang telah memuat turun resume dengan membolehkan perkongsian awam."

#: apps/client/src/pages/public/error.tsx:20
msgid "You don't have permission to access this page."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:87
msgid "You have the option to <0>obtain your own OpenAI API key</0>. This key empowers you to leverage the API as you see fit. Alternatively, if you wish to disable the AI features in Reactive Resume altogether, you can simply remove the key from your settings."
msgstr "Anda mempunyai pilihan untuk <0>mendapatkan kunci API OpenAI anda sendiri</0>. Kunci ini membolehkan anda menggunakan API sebagaimana yang anda lihat sesuai. Atau, jika anda ingin menyahdayakan ciri-ciri KI dalam Resume Reaktif sepenuhnya, anda boleh membuang kunci itu dari tetapan anda."

#: apps/client/src/pages/auth/verify-email/page.tsx:50
msgid "You should have received an email from <0>Reactive Resume</0> with a link to verify your account."
msgstr "Anda sepatutnya telah menerima emel daripada <0>Reactive Resume</0> dengan pautan untuk mengesahkan akaun anda."

#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/forgot-password/page.tsx:54
msgid "You've got mail!"
msgstr "Anda telah menerima emel!"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:52
msgid "Your account and all your data has been deleted successfully. Goodbye!"
msgstr "Akaun dan semua data anda telah berjaya dipadam. Selamat tinggal!"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:191
msgid "Your API key is securely stored in the browser's local storage and is only utilized when making requests to OpenAI via their official SDK. Rest assured that your key is not transmitted to any external server except when interacting with OpenAI's services."
msgstr "Kunci API anda disimpan dengan selamat dalam storan tempatan pelayar dan hanya digunakan apabila membuat permintaan kepada OpenAI melalui SDK rasmi mereka. Kita yakin bahawa kunci anda tidak dihantar ke mana-mana pelayan luar kecuali semasa berinteraksi dengan perkhidmatan OpenAI."

#: apps/client/src/pages/auth/verify-email/page.tsx:28
msgid "Your email address has been verified successfully."
msgstr "Alamat emel anda telah disahkan dengan berjaya."

#: apps/client/src/services/openai/client.ts:11
msgid "Your OpenAI API Key has not been set yet. Please go to your account settings to enable OpenAI Integration."
msgstr "Kunci API OpenAI anda belum ditetapkan. Sila pergi ke tetapan akaun anda untuk membolehkan Integrasi OpenAI."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:56
msgid "Your password has been updated successfully."
msgstr "Kata laluan anda telah berjaya dikemas kini."

#: apps/client/src/pages/builder/_components/toolbar.tsx:112
msgid "Zoom In"
msgstr "Zum dalam"

#: apps/client/src/pages/builder/_components/toolbar.tsx:118
msgid "Zoom Out"
msgstr "Zum keluar"

