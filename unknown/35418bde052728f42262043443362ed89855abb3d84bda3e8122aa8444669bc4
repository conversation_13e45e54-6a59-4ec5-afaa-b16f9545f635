msgid ""
msgstr ""
"POT-Creation-Date: 2023-11-10 13:15+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: sq\n"
"Project-Id-Version: reactive-resume\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-02-03 09:13\n"
"Last-Translator: \n"
"Language-Team: Albanian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: reactive-resume\n"
"X-Crowdin-Project-ID: 503410\n"
"X-Crowdin-Language: sq\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 494\n"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:171
msgid "You have enabled two-factor authentication successfully."
msgstr "Ju keni aktivizuar me sukses vë<PERSON><PERSON>min me dy faktor<PERSON>."

#: apps/client/src/pages/home/<USER>/features/index.tsx:57
msgid "{templatesCount} resume templates to choose from"
msgstr "{templatesCount} shabllone të CV-së"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:142
msgid "{value, plural, one {Column} other {Columns}}"
msgstr "{value, plural, one {Kolonë} other {Kolona}}"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:20
msgid "<0>I built Reactive Resume mostly by myself during my spare time, with a lot of help from other great open-source contributors.</0><1>If you like the app and want to support keeping it free forever, please donate whatever you can afford to give.</1>"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:51
msgid "<0>I'm sure the app is not perfect, but I'd like for it to be.</0><1>If you faced any issues while creating your resume, or have an idea that would help you and other users in creating your resume more easily, drop an issue on the repository or send me an email about it.</1>"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:201
msgid "<0>Note: </0>By utilizing the OpenAI API, you acknowledge and accept the <1>terms of use</1> and <2>privacy policy</2> outlined by OpenAI. Please note that Reactive Resume bears no responsibility for any improper or unauthorized utilization of the service, and any resulting repercussions or liabilities solely rest on the user."
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:85
msgid "<0>The community has spent a lot of time writing the documentation for Reactive Resume, and I'm sure it will help you get started with the app.</0><1>There are also a lot of examples to help you get started, and features that you might not know about which could help you build your perfect resume.</1>"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:140
msgid "<0>Two-factor authentication is currently disabled.</0> You can enable it by adding an authenticator app to your account."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:133
msgid "<0>Two-factor authentication is enabled.</0> You will be asked to enter a code every time you sign in."
msgstr ""

#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/home/<USER>/hero/index.tsx:40
msgid "A free and open-source resume builder"
msgstr "Një ndërtues i CV-së falas dhe open source"

#: apps/client/src/pages/home/<USER>/footer.tsx:21
#: apps/client/src/pages/home/<USER>/hero/index.tsx:45
msgid "A free and open-source resume builder that simplifies the process of creating, updating, and sharing your resume."
msgstr "Një ndërtues i CV-së, falas dhe open-source që thjeshtëson procesin e krijimit, përditësimit dhe shpërndarjes së CV-së tuaj."

#: apps/client/src/pages/builder/_components/toolbar.tsx:59
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:29
msgid "A link has been copied to your clipboard."
msgstr "Një link është kopjuar në clipboard."

#: apps/client/src/components/copyright.tsx:29
msgid "A passion project by <0>Amruth Pillai</0>"
msgstr "Një projekt pasioni nga <0>Amruth Pillai</0>"

#: apps/client/src/pages/auth/forgot-password/page.tsx:57
msgid "A password reset link should have been sent to your inbox, if an account existed with the email you provided."
msgstr "Një link për rivendosjes së fjalëkalimit ështe dërguar përmes emailit nëse një llogari me email-in e dhëne ekziston."

#: apps/client/src/services/errors/translate-error.ts:43
msgid "A resume with this slug already exists, please pick a different unique identifier."
msgstr "Një CV me këtë link të normalizuar veçse ekziston, ju lutemi zgjidhni një link tjetër unik."

#: apps/client/src/services/errors/translate-error.ts:10
msgid "A user with this email address and/or username already exists."
msgstr "Një përdorues me këtë adresë emaili dhe/ose emër përdoruesi ekziston."

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:43
msgid "A4"
msgstr "A4"

#. Helper text to let the user know what filetypes are accepted. {accept} can be .pdf or .json.
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:270
msgid "Accepts only {accept} files"
msgstr "Prano vetëm skedarët {accept}"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:105
msgid "Account"
msgstr "Llogaria"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:175
msgid "Add a custom field"
msgstr "Shto një fushë të personalizuar"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:119
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:170
msgctxt "For example, add a new work experience, or add a new profile."
msgid "Add a new item"
msgstr "Shto një vlerë të re"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:91
msgid "Add a new item"
msgstr "Shto një vlerë të re"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:146
#: apps/client/src/pages/builder/sidebars/left/index.tsx:263
msgid "Add a new section"
msgstr "Shto një rubrikë të re"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:261
msgid "Add New Page"
msgstr "Shto një faqe të re"

#: apps/client/src/components/ai-actions.tsx:79
msgid "AI"
msgstr "AI"

#: apps/client/src/pages/auth/register/page.tsx:71
msgid "Already have an account?"
msgstr "Posedoni një llogari?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:144
msgid "An error occurred while validating the file."
msgstr "Ndodhi një gabim gjatë verifikimit të skedarit."

#: apps/client/src/pages/public/error.tsx:23
msgid "An internal server error occurred."
msgstr ""

#: apps/client/src/pages/public/error.tsx:32
msgid "An unexpected error occurred."
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:134
msgid "and many more..."
msgstr "dhe shume te tjera..."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:57
msgid "Anyone with the link can view and download the resume."
msgstr "Çdokush që ka linkun mund të shikojë dhe shkarkojë CV-në."

#: apps/client/src/pages/builder/_components/toolbar.tsx:60
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:30
msgid "Anyone with this link can view and download the resume. Share it on your profile or with recruiters."
msgstr "Çdokush që ka këtë link mund ta shikojë dhe shkarkojë CV-në. Shprëndani atë në profilin tuaj ose me rekrutuesit."

#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:41
msgid "Apply Custom CSS"
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:128
msgid "Are you sure you want to delete this item?"
msgstr "Jeni i sigurt se dëshironi të fshini këtë vlerë?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:149
msgid "Are you sure you want to delete your resume?"
msgstr "Jeni i sigurt se dëshironi të fshini CV-në tuaj?"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:125
msgid "Are you sure you want to disable two-factor authentication?"
msgstr "Jeni i sigurt që dëshironi të çaktivizoni vërtetimin me dy faktorë?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:38
msgid "Are you sure you want to lock this resume?"
msgstr "Je i sigurt që dëshiron ta kyçësh këtë CV?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:39
msgid "Are you sure you want to unlock this resume?"
msgstr "Je i sigurt që dëshiron ta çkyçësh këtë CV?"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Are you sure?"
msgstr "A jeni të sigurt?"

#. For example, Computer Science or Business Administration
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:73
msgid "Area of Study"
msgstr "Fusha e Studimit"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:79
msgid "Aspect Ratio"
msgstr "Proporcioni"

#: apps/client/src/pages/home/<USER>/features/index.tsx:51
msgid "Available in {languagesCount} languages"
msgstr "E ofruar në {languagesCount} gjuhë"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:53
msgid "Awarder"
msgstr "Dhurues"

#: apps/client/src/pages/auth/backup-otp/page.tsx:99
#: apps/client/src/pages/auth/forgot-password/page.tsx:100
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:243
msgid "Back"
msgstr "Mbrapa"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:73
msgid "Background Color"
msgstr "Ngjyra e sfondit"

#: apps/client/src/pages/auth/backup-otp/page.tsx:75
msgid "Backup Code"
msgstr "Kodi rezervë"

#: apps/client/src/pages/auth/backup-otp/page.tsx:81
msgid "Backup Codes may contain only lowercase letters or numbers, and must be exactly 10 characters."
msgstr "Kodet rezervë mund të përmbajnë vetëm shkronja të vogla ose numra dhe duhet të kenë saktësisht 10 karaktere."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:132
msgid "Base URL"
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/index.tsx:55
msgctxt "The basics section of a resume consists of User's Picture, Full Name, Location etc."
msgid "Basics"
msgstr "Bazat"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:21
msgid "Basics"
msgstr "Bazat"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:191
msgid "Border"
msgstr "Vijat kufizuese"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:124
msgid "Border Radius"
msgstr "Rrezja e këndeve"

#: apps/client/src/pages/public/page.tsx:93
msgid "Built with"
msgstr "E ndërtuar me"

#: apps/client/src/components/copyright.tsx:27
#: apps/client/src/pages/home/<USER>/contributors/index.tsx:20
msgid "By the community, for the community."
msgstr "Nga komuniteti, për komunitetin."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:135
#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:49
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:156
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:137
msgid "Cancel"
msgstr "Anulo"

#: apps/client/src/components/ai-actions.tsx:103
#: apps/client/src/components/ai-actions.tsx:106
msgid "Casual"
msgstr "Jo formale"

#: apps/client/src/pages/builder/_components/toolbar.tsx:130
msgid "Center Artboard"
msgstr "Centro panelin"

#: apps/client/src/pages/auth/reset-password/page.tsx:99
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:115
msgid "Change Password"
msgstr "Ndrysho fjalëkalimin"

#: apps/client/src/components/ai-actions.tsx:97
msgid "Change Tone"
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:186
msgid "Changed your mind about the name? Give it a new one."
msgstr "Ndryshove mendjen për emrin? Riemëro."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:70
msgid "Check your email for the confirmation link to update your email address."
msgstr "Për të përditësuar adresën tuaj të emailit kontrolloni emailin tuaj për linkun e konfirmimit."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:144
msgid "Circle"
msgstr "Rretho"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:249
msgid "Close"
msgstr "Mbyll"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:201
msgid "Code"
msgstr "Kodi"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:52
msgid "Code must be exactly 6 digits long."
msgstr "Kodi duhet të jetë saktësisht 6 shifra."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:136
msgid "Columns"
msgstr "Kolonat"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:39
msgid "Company"
msgstr "Kompania"

#: apps/client/src/components/ai-actions.tsx:115
#: apps/client/src/components/ai-actions.tsx:118
msgid "Confident"
msgstr "Të sigurt"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:234
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:246
msgid "Continue"
msgstr "Vazhdo"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:96
msgid "Copy"
msgstr "Kopjo"

#: apps/client/src/pages/builder/_components/toolbar.tsx:164
msgid "Copy Link to Resume"
msgstr "Kopjo linkun e CV-së"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:78
msgid "Copy to Clipboard"
msgstr "Kopjo në Clipboard"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:179
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:244
msgid "Create"
msgstr "Krijo"

#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/register/page.tsx:69
msgid "Create a new account"
msgstr "Krijo një llogari të re"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:163
msgid "Create a new item"
msgstr "Krijo një vlerë të re"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:178
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:18
msgid "Create a new resume"
msgstr "Krijo një CV të re"

#: apps/client/src/pages/auth/login/page.tsx:65
msgctxt "This is a link to create a new account"
msgid "Create one now"
msgstr "Regjistrohu"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:259
msgid "Create Sample Resume"
msgstr "Krijo një CV nga shembujt"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:82
msgid "Current Password"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/index.tsx:93
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:27
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:28
msgid "Custom CSS"
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:62
msgid "Custom resume sections"
msgstr "Rubrikat e personalizuara të CV-së"

#: apps/client/src/stores/resume.ts:47
msgid "Custom Section"
msgstr "Rubrikë e personalizuar"

#: apps/client/src/pages/home/<USER>/features/index.tsx:60
msgid "Customisable colour palettes"
msgstr "Paleta ngjyrash të personalizuara"

#: apps/client/src/pages/home/<USER>/features/index.tsx:61
msgid "Customisable layouts"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:62
msgid "Danger Zone"
msgstr "Zonë Rreziku"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:87
msgid "Dark"
msgstr "E errët"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:67
msgid "Date"
msgstr "Data"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:110
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:72
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:67
msgid "Date or Date Range"
msgstr "Data ose intervali"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:137
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:158
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:121
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:127
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:172
msgid "Delete"
msgstr "Fshi"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:79
#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Delete Account"
msgstr "Fshi llogarinë"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:50
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:53
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:63
msgid "Description"
msgstr "Përshkrimi"

#: apps/client/src/pages/home/<USER>/features/index.tsx:58
msgid "Design single/multi page resumes"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:139
msgid "Disable"
msgstr "Çaktivizo"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:154
msgid "Disable 2FA"
msgstr "Çaktivizo vërtetimin me dy faktorë"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:302
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:220
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:134
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:118
msgid "Discard"
msgstr "Braktis"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:105
msgid "Documentation"
msgstr "Dokumentacioni"

#: apps/client/src/pages/auth/login/page.tsx:62
msgid "Don't have an account?"
msgstr "Nuk keni akoma një llogari?"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:83
msgid "Don't know where to begin? Hit the docs!"
msgstr "Nuk dini ku të filloni? Shiko dokumentacionin!"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:107
msgid "Don't see your language? <0>Help translate the app.</0>"
msgstr "Nuk e shihni gjuhën tuaj? <0>Ndihmoni në përkthimin e aplikacionit.</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:40
msgid "Donate to Reactive Resume"
msgstr "Dhuro në Reactive Resume"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:56
msgid "Download a JSON snapshot of your resume. This file can be used to import your resume in the future, or can even be shared with others to collaborate."
msgstr "Shkarkoni një CV-në tuaj në formatin JSON. Ky skedar mund të përdoret për të importuar CV-në tuaj në të ardhmen, madje edhe mund të ndahet me të tjerët për të bashkëpunuar."

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:74
msgid "Download a PDF of your resume. This file can be used to print your resume, send it to recruiters, or upload on job portals."
msgstr "Shkarkoni CV-në tuaj si PDF. Ky skedar mund të përdoret për të printuar CV-në tuaj, për ta dërguar te rekrutuesit ose për ta ngarkuar në portalet e punës."

#: apps/client/src/pages/builder/_components/toolbar.tsx:176
msgid "Download PDF"
msgstr "Shkarko PDF"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:58
msgid "Downloads"
msgstr "Shkarkimet"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:181
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:246
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:95
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:156
msgid "Duplicate"
msgstr "Dyfisho"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:165
msgid "Duplicate an existing item"
msgstr "Dupliko një vlerë ekzistuese"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:180
msgid "Duplicate an existing resume"
msgstr "Dyfisho një CV ekzistuese"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:92
msgid "Edit"
msgstr "Përpuno"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:169
msgid "Effects"
msgstr "Efektet"

#: apps/client/src/pages/auth/forgot-password/page.tsx:82
#: apps/client/src/pages/auth/login/page.tsx:90
#: apps/client/src/pages/auth/register/page.tsx:141
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:54
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:181
msgid "Email"
msgstr "Email"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:163
msgid "Enable 2FA"
msgstr "Aktivizo vërtetimin me dy faktorë"

#: apps/client/src/pages/auth/reset-password/page.tsx:67
msgid "Enter a new password below, and make sure it's secure."
msgstr "Zgjedh një fjalëkalim të ri. Sigurohuni që të jetë i sigurtë."

#: apps/client/src/pages/auth/backup-otp/page.tsx:59
msgid "Enter one of the 10 backup codes you saved when you enabled two-factor authentication."
msgstr "Jep një nga 10 koded rezervë që i keni ruajtur gjatë aktivizimit të vërtetimit me dy faktorë."

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:63
msgid "Enter Phosphor Icon"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:170
msgid "Enter the 6-digit code from your authenticator app to verify that 2FA has been setup correctly."
msgstr "Jep kodin 6 shifror nga aplikacioni authenticator për të verifikuar që vertetimi me dy faktorë është konfiguruar si duhet."

#: apps/client/src/pages/auth/verify-otp/page.tsx:60
msgid "Enter the one-time password provided by your authenticator app below."
msgstr "Jep fjalkalimin njëpërdorimësh nga aplikacioni authenticator."

#: apps/client/src/pages/auth/forgot-password/page.tsx:70
msgid "Enter your email address and we will send you a link to reset your password if the account exists."
msgstr "Jepni adresën tuaj të emailit dhe ne do t'ju dërgojmë një link për të ndryshuar fjalëkalimin nëse llogaria ekziston."

#: apps/client/src/pages/public/error.tsx:46
msgid "Error {statusCode}"
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:283
msgid "Errors"
msgstr "Problemet"

#: apps/client/src/pages/home/<USER>/support/index.tsx:78
msgid "Even if you're not in a position to contribute financially, you can still make a difference by giving the GitHub repository a star, spreading the word to your friends, or dropping a quick message to let me know how Reactive Resume has helped you. Your feedback and support are always welcome and much appreciated!"
msgstr ""

#: apps/client/src/pages/home/<USER>/templates/index.tsx:12
msgid "Explore the templates available in Reactive Resume and view the resumes crafted with them. They could also serve as examples to help guide the creation of your next resume."
msgstr "Eksploroni shabllonet e disponueshme në Reactive Resume dhe shikoni CV-të e krijuara me to. Ato mund të ndihmojnë në krijimin e CV-së tuaj të ardhshme."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:121
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:39
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:40
msgid "Export"
msgstr "Exporto"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:255
msgid "File"
msgstr "Skedari"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:221
msgid "Filetype"
msgstr "Lloji i skedarit"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:38
msgid "Finally,"
msgstr "Së fundi,"

#: apps/client/src/components/ai-actions.tsx:90
msgid "Fix Spelling & Grammar"
msgstr "Rregulloni drejtshkrimin dhe gramatikën"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:107
msgid "Font Family"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:148
msgid "Font Size"
msgstr "Madhësia e germave"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:122
msgid "Font Subset"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:134
msgid "Font Variants"
msgstr "Varianti i germave"

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:35
msgid "For example, information regarding which companies you sent this resume to or the links to the job descriptions can be noted down here."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:182
msgid "Forget"
msgstr "Harro"

#: apps/client/src/pages/auth/login/page.tsx:131
msgid "Forgot Password?"
msgstr "Keni haruar fjalëkalimin?"

#: apps/client/src/pages/auth/forgot-password/page.tsx:68
msgid "Forgot your password?"
msgstr "Keni harruar fjalëkalimin?"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:32
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:40
msgid "Format"
msgstr "Formati"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:49
msgid "Found a bug, or have an idea for a new feature?"
msgstr "Keni gjetur një gabim, apo keni një ide për një veçori të re?"

#: apps/client/src/pages/home/<USER>/features/index.tsx:46
msgid "Free, forever"
msgstr "Falas, përgjithmonë"

#: apps/client/src/components/ai-actions.tsx:121
#: apps/client/src/components/ai-actions.tsx:124
msgid "Friendly"
msgstr "Miqësor"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:31
msgid "Full Name"
msgstr "Emri i plotë"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:202
msgid "Generate a random title for your resume"
msgstr ""

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:32
msgid "Get Started"
msgstr "Le të fillojmë"

#: apps/client/src/pages/auth/_components/social-auth.tsx:18
msgid "GitHub"
msgstr "GitHub"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:12
msgid "GitHub Stars"
msgstr "Yjet në GitHub"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:187
msgid "Give your old resume a new name."
msgstr "Jepini CV-së tuaj të vjetër një emër të ri."

#: apps/client/src/pages/auth/verify-email/page.tsx:67
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:18
msgid "Go to Dashboard"
msgstr "Shko te paneli kryesor"

#: apps/client/src/pages/public/error.tsx:55
msgid "Go to home"
msgstr ""

#: apps/client/src/pages/auth/_components/social-auth.tsx:31
msgid "Google"
msgstr "Google"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:202
msgid "Grayscale"
msgstr "Nuancat gri"

#: apps/client/src/pages/dashboard/resumes/page.tsx:43
msgid "Grid"
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:43
msgid "Headline"
msgstr "Titulli"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:107
msgid "Here, you can update your account information such as your profile picture, name and username."
msgstr "Këtu mund të përditësoni informacionin e llogarisë suaj, si fotografinë e profilit, emrin dhe emrin e përdoruesit."

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:68
msgid "Here, you can update your profile to customize and personalize your experience."
msgstr "Këtu mund të përditësoni profilin tuaj për të personalizuar përvojën tuaj."

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:80
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:94
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:180
msgid "Hidden"
msgstr "E fshehur"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Hide"
msgstr "Fshih"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:192
msgid "Hide Icons"
msgstr "Fshih ikonat"

#: apps/client/src/pages/auth/login/page.tsx:115
#: apps/client/src/pages/auth/register/page.tsx:168
#: apps/client/src/pages/auth/reset-password/page.tsx:88
msgid "Hold <0>Ctrl</0> to display your password temporarily."
msgstr "Shtyp <0>Ctrl</0> për të shfaqur fjalëkalimin tuaj përkohësisht."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:93
msgid "Horizontal"
msgstr "Horizontal"

#: apps/client/src/pages/home/<USER>/features/index.tsx:67
msgid "Host your resume publicly"
msgstr "Bëjeni CV-në tuaj publike"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:70
msgid "I always love to hear from the users of Reactive Resume with feedback or support. Here are some of the messages I've received. If you have any feedback, feel free to drop me an email at <0>{email}</0>."
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:83
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:53
msgid "Icon"
msgstr "Ikona"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:47
msgid "If this app has helped you with your job hunt, let me know by reaching out through <0>this contact form</0>."
msgstr "Nëse ky aplikacion ju ka ndihmuar të gjeni punë, më njoftoni përmes <0>këtij formulari</0>."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:128
msgid "If you disable two-factor authentication, you will no longer be required to enter a verification code when logging in."
msgstr ""

#: apps/client/src/pages/home/<USER>/support/index.tsx:59
msgid "If you're multilingual, we'd love your help in bringing the app to more languages and communities. Don't worry if you don't see your language on the list - just give me a shout-out on GitHub, and I'll make sure to include it. Ready to get started? Jump into translation over at Crowdin by clicking the link below."
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:309
msgid "Import"
msgstr "Importo"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:208
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:17
msgid "Import an existing resume"
msgstr "Importoni një CV ekzistuese"

#: apps/client/src/components/ai-actions.tsx:85
msgid "Improve Writing"
msgstr "Përmirëso të shkruarit"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:188
msgid "In case you are unable to scan this QR Code, you can also copy-paste this link into your authenticator app."
msgstr "Në rast se nuk jeni në gjendje të skanoni këtë kod QR, mund ta kopjoni linkun nga aplikacioni authenticator dhe ta ngjisni atë këtu."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:67
msgid "In this section, you can change your password and enable/disable two-factor authentication."
msgstr "Në këtë seksion, ju mund të ndryshoni fjalëkalimin tuaj dhe të aktivizoni/çaktivizoni vërtetimin me dy faktorë."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:64
msgid "In this section, you can delete your account and all the data associated to your user, but please keep in mind that <0>this action is irreversible</0>."
msgstr "Në këtë seksion, ju mund të fshini llogarinë tuaj dhe të gjitha të dhënat e lidhura me përdoruesin tuaj, por keni kujdes se <0>ky veprim është i pakthyeshëm</0>."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:135
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:116
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:117
msgid "Information"
msgstr "Informacion"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:39
msgid "Institution"
msgstr "Institucioni"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:53
msgid "Issuer"
msgstr "Lëshuesi"

#: apps/client/src/services/errors/translate-error.ts:7
msgid "It doesn't look like a user exists with the credentials you provided."
msgstr "Nuk ekziston një përdorues me kredencialet e dhëna."

#: apps/client/src/services/errors/translate-error.ts:37
msgid "It looks like the backup code you provided is invalid or used. Please try again."
msgstr "Kodi rezervë që keni dhënë është i pavlefshëm ose është përdorur. Ju lutemi provoni përsëri."

#: apps/client/src/services/errors/translate-error.ts:19
msgid "It looks like the reset token you provided is invalid. Please try restarting the password reset process again."
msgstr "Kodi që keni dhënë është i pavlefshëm. Ju lutemi provoni përsëri procesin e rivendosjes së fjalëkalimit."

#: apps/client/src/services/errors/translate-error.ts:46
msgid "It looks like the resume you're looking for doesn't exist."
msgstr "Duket sikur CV-ja që po kërkoni nuk ekziston."

#: apps/client/src/services/errors/translate-error.ts:34
msgid "It looks like the two-factor authentication code you provided is invalid. Please try again."
msgstr "Kodi i vërtetimit me dy faktorë që keni dhënë është i pavlefshëm. Ju lutemi provoni përsëri."

#: apps/client/src/services/errors/translate-error.ts:22
msgid "It looks like the verification token you provided is invalid. Please try restarting the verification process again."
msgstr "Kodi i verifikimit që ke dhënë është i pavlefshëm. Ju lutemi rinisni procesin e verifikimit."

#: apps/client/src/services/errors/translate-error.ts:25
msgid "It looks like your email address has already been verified."
msgstr "Emaili juaj veçse është verifikuar."

#: apps/client/src/pages/auth/register/page.tsx:101
msgctxt "Localized version of a placeholder name. For example, Max Mustermann in German or Jan Kowalski in Polish."
msgid "John Doe"
msgstr "Filan Fisteku"

#: apps/client/src/pages/auth/register/page.tsx:123
msgctxt "Localized version of a placeholder username. For example, max.mustermann in German or jan.kowalski in Polish."
msgid "john.doe"
msgstr "filan.fisteku"

#: apps/client/src/pages/auth/register/page.tsx:145
msgctxt "Localized version of a placeholder email. For example, <EMAIL> in <NAME_EMAIL> in Polish."
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:54
msgid "JSON"
msgstr "JSON"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:63
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:109
msgid "Keywords"
msgstr "Fjalët kyçe"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:42
#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:52
msgid "Label"
msgstr "Etiketa"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:101
msgid "Language"
msgstr "Gjuha"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:83
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:139
msgid "Last updated {lastUpdated}"
msgstr "Përditësuar së fundmi me {lastUpdated}"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:72
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:197
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:198
msgid "Layout"
msgstr ""

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:38
msgid "Learn more"
msgstr "Mëso më shumë"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:44
msgid "Letter"
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:64
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:77
msgid "Level"
msgstr "Niveli"

#: apps/client/src/components/copyright.tsx:16
msgid "Licensed under <0>MIT</0>"
msgstr "Licencuar sipas <0>MIT</0>"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:86
msgid "Light"
msgstr "E çelët"

#: apps/client/src/pages/home/<USER>/features/index.tsx:69
msgid "Light or dark theme"
msgstr "Pamje e çelët apo e errët"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:165
msgid "Line Height"
msgstr "Lartësia e rreshtit"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:22
msgid "LinkedIn, JSON Resume, etc."
msgstr "LinkedIn, JSON CV, etj."

#: apps/client/src/pages/dashboard/resumes/page.tsx:47
msgid "List"
msgstr "Listë"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:86
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:81
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:93
msgid "Location"
msgstr "Vendndodhja"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:51
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:166
msgid "Lock"
msgstr "Kyç"

#: apps/client/src/pages/home/<USER>/features/index.tsx:64
msgid "Lock a resume to prevent editing"
msgstr "Kyç një CV për të parandaluar modifikimin"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:43
msgid "Locking a resume will prevent any further changes to it. This is useful when you have already shared your resume with someone and you don't want to accidentally make any changes to it."
msgstr "Duke kyçur një CV ju parandaloni çdo ndryshim të mëtejshëm në të. Kjo është e dobishme kur tashmë e keni ndarë CV-në tuaj me dikë dhe nuk dëshironi të bëni ndonjë ndryshim aksidentalisht."

#: apps/client/src/components/user-options.tsx:38
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:23
msgid "Logout"
msgstr "Çkyçu"

#: apps/client/src/pages/auth/verify-otp/page.tsx:64
msgid "Lost your device?"
msgstr "Keni humbur pajisjen tuaj?"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:247
msgid "Main"
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:59
msgid "Manage multiple resumes"
msgstr "Ndrysho disa CV"

#. The month and year should be uniform across all languages.
#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:71
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:69
msgid "March 2023"
msgstr "Mars 2023"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:112
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:74
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:103
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:69
msgid "March 2023 - Present"
msgstr "Mars 2023 - tani"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:50
msgid "Margin"
msgstr "Largësia e kufinjëve"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:158
msgid "Max Tokens"
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:48
msgid "MIT License"
msgstr "Licenca MIT"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:145
msgid "Model"
msgstr ""

#: apps/client/src/pages/auth/register/page.tsx:98
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:59
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:48
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:36
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:49
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:88
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:153
msgid "Name"
msgstr "Emri"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:39
msgctxt "Name of the Certification"
msgid "Name"
msgstr "Emri"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:40
msgid "Network"
msgstr "Rrjeti"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:96
msgid "New Password"
msgstr "Fjalëkalim i ri"

#: apps/client/src/components/locale-combobox.tsx:45
msgid "No results found"
msgstr "Nuk u gjetën rezultate"

#: apps/client/src/pages/home/<USER>/features/index.tsx:49
msgid "No user tracking or advertising"
msgstr "Pa ndjekje ose reklamim të përdoruesit"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:133
msgid "Note: This will make your account less secure."
msgstr "Kujdes: Kjo mund ta zvoglojë sigurinë e llogarisë tuaj."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:128
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:16
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:17
msgid "Notes"
msgstr "Shënime"

#: apps/client/src/pages/auth/verify-otp/page.tsx:82
msgid "One-Time Password"
msgstr "Fjalkalim njëpërdorimësh"

#: apps/client/src/components/ai-actions.tsx:56
#: apps/client/src/libs/axios.ts:30
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:188
#: apps/client/src/services/resume/print.tsx:26
msgid "Oops, the server returned an error."
msgstr "Serveri hasi në një problem."

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:97
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:77
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:148
msgid "Open"
msgstr "Hap"

#: apps/client/src/pages/home/<USER>/features/index.tsx:47
msgid "Open Source"
msgstr "Open Source"

#: apps/client/src/services/openai/change-tone.ts:35
#: apps/client/src/services/openai/fix-grammar.ts:33
#: apps/client/src/services/openai/improve-writing.ts:33
msgid "OpenAI did not return any choices for your text."
msgstr "OpenAI nuk gjeti asnjë zgjedhje për tekstin tuaj."

#: apps/client/src/pages/home/<USER>/features/index.tsx:52
msgid "OpenAI Integration"
msgstr "Integrimi me OpenAI"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:119
msgid "OpenAI/Ollama API Key"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:79
msgid "OpenAI/Ollama Integration"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:67
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:182
msgid "Options"
msgstr "Mundësitë"

#: apps/client/src/pages/auth/layout.tsx:47
msgctxt "The user can either login with email/password, or continue with GitHub or Google."
msgid "or continue with"
msgstr "ose vazhdoni me"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:39
msgid "Organization"
msgstr "Organizata"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:100
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:25
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:26
msgid "Page"
msgstr "Faqja"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:228
msgid "Page {pageNumber}"
msgstr ""

#: apps/client/src/pages/auth/login/page.tsx:110
#: apps/client/src/pages/auth/register/page.tsx:163
#: apps/client/src/pages/auth/reset-password/page.tsx:83
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:73
msgid "Password"
msgstr "Fjalëkalimi"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:72
msgid "PDF"
msgstr "PDF"

#: apps/client/src/pages/home/<USER>/features/index.tsx:63
msgid "Personal notes for each resume"
msgstr "Shënime personale për çdo CV"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:81
msgid "Phone"
msgstr "Telefoni"

#: apps/client/src/pages/auth/layout.tsx:76
msgid "Photograph by Patrick Tomasso"
msgstr "Fotografi nga Patrick Tomasso"

#: apps/client/src/pages/home/<USER>/features/index.tsx:66
msgid "Pick any font from Google Fonts"
msgstr "Zgjidh një font nga Google Fonts"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/section.tsx:69
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:121
msgid "Picture"
msgstr "Foto"

#: apps/client/src/pages/auth/verify-email/page.tsx:59
msgid "Please note that this step is completely optional."
msgstr "Ju lutem kini parasysh që ky hap nuk është i detyrueshëm."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:225
msgid "Please select a file type"
msgstr "Ju lutemi zgjidhni një lloj skedari"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:228
msgid "Please store your backup codes in a secure location. You can use one of these one-time use codes to login in case you lose access to your authenticator app."
msgstr "Ju lutemi ruani kodet rezervë në një vend të sigurt. Ju mund të përdorni një nga këto kode një-përdorimëshe për t'u kyçur në rast se humbni çasjen në aplikacionin authenticator."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:99
msgid "Portrait"
msgstr "Portret"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:54
msgctxt "Position held at a company, for example, Software Engineer"
msgid "Position"
msgstr "Pozicioni"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:53
msgid "Position"
msgstr "Pozicioni"

#: apps/client/src/pages/home/<USER>/features/index.tsx:96
msgid "Powered by"
msgstr "Mundësuar nga"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:94
msgid "Powered by <0>Simple Icons</0>"
msgstr "Mundësuar nga <0>Simple Icons</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:43
msgid "Primary Color"
msgstr "Ngjyra kryesore"

#: apps/client/src/pages/home/<USER>/footer.tsx:50
msgid "Privacy Policy"
msgstr ""

#: apps/client/src/components/ai-actions.tsx:109
#: apps/client/src/components/ai-actions.tsx:112
msgid "Professional"
msgstr "Profesional"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:66
msgid "Profile"
msgstr "Profili"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:55
msgid "Public"
msgstr "Publik"

#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:53
msgid "Publisher"
msgstr "Botues"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:69
msgid "Raise an issue"
msgstr "Krijo një Issue në GitHub"

#: apps/client/src/components/copyright.tsx:35
#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/builder/page.tsx:60
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/home/<USER>/footer.tsx:18
#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/public/page.tsx:74
#: apps/client/src/pages/public/page.tsx:95
msgid "Reactive Resume"
msgstr "Reactive Resume"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:39
msgid "Reactive Resume has helped people land jobs at these great companies:"
msgstr "Reactive Resume i ka ndihmuar njerëzit të gjejnë punë në këto kompani të mëdha:"

#: apps/client/src/pages/home/<USER>/support/index.tsx:12
msgid "Reactive Resume is a free and open-source project crafted mostly by me, and your support would be greatly appreciated. If you're inclined to contribute, and only if you can afford to, consider making a donation through any of the listed platforms. Additionally, donations to Reactive Resume through Open Collective are tax-exempt, as the project is fiscally hosted by Open Collective Europe."
msgstr "Reactive Resume është një projekt falas dhe open-source i krijuar kryesisht nga unë. Mbështetja juaj do të vlerësohej shumë. Nëse jeni të prirur të ndihmoni, por vetëm nëse keni mundësi, mund të bëni një donacion përmes platformave të listuara. Donacionet për Reactive Resume përmes Open Collective janë të përjashtuara nga taksat, pasi projekti është i organizuar fiskalisht nga Open Collective Europe."

#: apps/client/src/pages/home/<USER>/features/index.tsx:107
msgid "Reactive Resume is a passion project of over 3 years of hard work, and with that comes a number of re-iterated ideas and features that have been built to (near) perfection."
msgstr ""

#: apps/client/src/pages/home/<USER>/contributors/index.tsx:22
msgid "Reactive Resume thrives thanks to its vibrant community. This project owes its progress to numerous individuals who've dedicated their time and skills. Below, we celebrate the coders who've enhanced its features on GitHub and the linguists whose translations on Crowdin have made it accessible to a broader audience."
msgstr ""

#: apps/client/src/pages/builder/_components/toolbar.tsx:89
msgid "Redo"
msgstr "Ribëj"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:100
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:157
msgid "Remove"
msgstr "Fshi"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:231
msgid "Remove Page"
msgstr "Fshi faqen"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:111
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:101
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:86
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:152
msgid "Rename"
msgstr "Riemërto"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:199
msgid "Resend email confirmation link"
msgstr "Ridërgo linun për konfirmimit të emailit"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:152
msgid "Reset"
msgstr "Rivendos"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:201
msgid "Reset Layout"
msgstr ""

#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/reset-password/page.tsx:65
msgid "Reset your password"
msgstr "Rivendosni fjalëkalimin tuaj"

#: apps/client/src/pages/builder/_components/toolbar.tsx:124
msgid "Reset Zoom"
msgstr "Kthe madhësinë si në fillim"

#: apps/client/src/pages/dashboard/_components/sidebar.tsx:86
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/resumes/page.tsx:37
msgid "Resumes"
msgstr "CV-të"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:14
msgid "Resumes Generated"
msgstr "CV-të u gjeneruan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:105
msgid "Rich in features, not in pricing."
msgstr "I pasura në veçori, jo në çmim."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:138
msgid "Rounded"
msgstr "E rrumbullakësuar"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:180
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:245
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:217
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:131
msgid "Save Changes"
msgstr "Ruaj ndryshimet"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Save Locally"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Saved"
msgstr ""

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:168
msgid "Scan the QR code below with your authenticator app to setup 2FA on your account."
msgstr "Skanoni kodin e mëposhtëm QR me aplikacionin tuaj authenticator për të konfiguruar vërtetimin me dy faktorë në llogarinë tuaj."

#. Score or honors for the degree, for example, CGPA or magna cum laude
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:92
msgid "Score"
msgstr "Vlerësimi"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Pan"
msgstr ""

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Zoom"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:111
msgid "Search for a font family"
msgstr "Kërkoni një familje të shkronjave"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:126
msgid "Search for a font subset"
msgstr "Kërkoni një nëngrup shkronjash"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:139
msgid "Search for a font variant"
msgstr "Kërkoni një variant të shkronjave"

#: apps/client/src/components/locale-combobox.tsx:41
msgid "Search for a language"
msgstr "Kërkoni një gjuhë"

#: apps/client/src/pages/home/<USER>/features/index.tsx:56
msgid "Secure with two-factor authentication"
msgstr "Sigurohuni duke përdorur vërtetim me dy faktorë"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:65
msgid "Security"
msgstr "Siguria"

#: apps/client/src/pages/home/<USER>/features/index.tsx:50
msgid "Self-host with Docker"
msgstr ""

#: apps/client/src/pages/auth/forgot-password/page.tsx:104
msgid "Send Email"
msgstr "Dërgo emailin"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:74
msgid "Send me a message"
msgstr "Më dërgo një mesazh"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:97
msgid "Separate Links"
msgstr ""

#: apps/client/src/components/user-options.tsx:32
#: apps/client/src/pages/dashboard/_components/sidebar.tsx:92
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/dashboard/settings/page.tsx:26
msgid "Settings"
msgstr "Parametrat"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:159
msgid "Setup two-factor authentication on your account"
msgstr "Konfiguro vërtetimin me dy faktorë në llogarinë tuaj"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:107
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:38
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:39
msgid "Sharing"
msgstr "Shpërndarja"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Show"
msgstr "Shfaq"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:78
msgid "Show Break Line"
msgstr "Shfaq fundin e faqes"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:91
msgid "Show Page Numbers"
msgstr "Shfaq numrin e faqes"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:248
msgid "Sidebar"
msgstr "Shiriti anësor"

#: apps/client/src/pages/auth/backup-otp/page.tsx:103
#: apps/client/src/pages/auth/login/page.tsx:127
#: apps/client/src/pages/auth/verify-otp/page.tsx:92
msgid "Sign in"
msgstr "Kyçu"

#: apps/client/src/pages/auth/register/page.tsx:74
msgid "Sign in now"
msgstr "Kyçu tani"

#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/login/page.tsx:60
msgid "Sign in to your account"
msgstr "Kyçu në llogarinë tuaj"

#: apps/client/src/pages/home/<USER>/features/index.tsx:55
msgid "Sign in with Email"
msgstr "Kyçu përmes email"

#: apps/client/src/pages/home/<USER>/features/index.tsx:53
msgid "Sign in with GitHub"
msgstr "Kyçu përmes GitHub"

#: apps/client/src/pages/home/<USER>/features/index.tsx:54
msgid "Sign in with Google"
msgstr "Kyçu përmes Google"

#: apps/client/src/pages/auth/register/page.tsx:179
msgid "Sign up"
msgstr "Regjistrohu"

#: apps/client/src/pages/auth/login/page.tsx:74
msgid "Signing in via email is currently disabled by the administrator."
msgstr ""

#: apps/client/src/pages/auth/register/page.tsx:82
msgid "Signups are currently disabled by the administrator."
msgstr "Regjistrimet janë çaktivizuar nga administratori."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:65
msgid "Size (in px)"
msgstr "Madhësia (në px)"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:228
msgid "Slug"
msgstr "Linku i normalizuar"

#: apps/client/src/services/errors/translate-error.ts:55
msgid "Something went wrong while grabbing a preview your resume. Please try again later or raise an issue on GitHub."
msgstr "Diçka dështoi gjatë shfaqjes paraprake të CV-së tuaj. Ju lutemi provoni përsëri më vonë ose ngrini një problem në GitHub."

#: apps/client/src/services/errors/translate-error.ts:52
msgid "Something went wrong while printing your resume. Please try again later or raise an issue on GitHub."
msgstr "Diçka dështoi gjatë printimit të CV-së tuaj. Ju lutemi provoni përsëri më vonë ose ngritni një problem në GitHub."

#: apps/client/src/services/errors/translate-error.ts:58
msgid "Something went wrong while processing your request. Please try again later or raise an issue on GitHub."
msgstr "Diçka dështoi gjatë përpunimit të kërkesës suaj. Ju lutemi provoni përsëri më vonë ose ngritni një problem në GitHub."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:87
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:132
msgid "Square"
msgstr "Katrori"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:23
msgid "Start building from scratch"
msgstr "Fillo të ndërtosh nga e para"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:185
msgid "Start building your resume by giving it a name."
msgstr "Filloni të ndërtoni CV-në tuaj duke i dhënë një emër."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:114
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:22
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:23
msgid "Statistics"
msgstr "Statistikat"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:38
msgid "Statistics are available only for public resumes."
msgstr "Statistikat janë të disponueshme vetëm për CV-të publike."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:162
msgid "Store your backup codes securely"
msgstr "Ruani kodet rezervë në mënyrë të sigurt"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:138
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:114
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:109
msgid "Summary"
msgstr "Përmbledhja"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:18
msgid "Support the app by donating what you can!"
msgstr "Mbështetni aplikacionin duke dhuruar atë që mundeni!"

#: apps/client/src/pages/home/<USER>/support/index.tsx:9
msgid "Supporting Reactive Resume"
msgstr "Mbështet Reactive Resume"

#: apps/client/src/pages/home/<USER>/features/index.tsx:65
msgid "Supports A4/Letter page formats"
msgstr "Mbështet formatet e faqeve A4"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:85
msgid "System"
msgstr "Sistemi"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:65
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:18
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:19
msgid "Template"
msgstr "Shabllon"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:9
msgid "Templates"
msgstr "Shabllonet"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:68
msgid "Testimonials"
msgstr "Dëshmitë"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:103
msgid "Text Color"
msgstr "Ngjyra e tekstit"

#: apps/client/src/pages/public/error.tsx:17
msgid "The page you're looking for doesn't exist."
msgstr ""

#: apps/client/src/pages/public/error.tsx:29
msgid "The request was invalid."
msgstr ""

#: apps/client/src/services/errors/translate-error.ts:49
msgid "The resume you want to update is locked, please unlock if you wish to make any changes to it."
msgstr "CV që dëshironi të përditësoni është e kyçur, ju lutemi çkyçeni nëse dëshironi para se ta ndryshoni."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:86
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:19
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:20
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:79
msgid "Theme"
msgstr "Tema"

#: apps/client/src/services/errors/translate-error.ts:40
msgid "There was an error connecting to the browser. Please make sure 'chrome' is running and reachable."
msgstr "Diçka dështoi gjatë lidhjes me shfletuesin. Sigurohu që \"chrome\" po funksionon dhe është i arritshëm."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:130
msgid "This action can be reverted by clicking on the undo button in the floating toolbar."
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:151
msgid "This action cannot be undone. This will permanently delete your resume and cannot be recovered."
msgstr ""

#: apps/client/src/services/errors/translate-error.ts:16
msgid "This email address is associated with an OAuth account. Please sign in with your OAuth provider."
msgstr ""

#: apps/client/src/pages/builder/_components/header.tsx:57
msgid "This resume is locked, please unlock to make further changes."
msgstr "Kjo CV është e kyçur, ju lutemi shkyçeni për të bërë ndryshime të mëtejshme."

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:23
msgid "This section is reserved for your personal notes specific to this resume. The content here remains private and is not shared with anyone else."
msgstr "Ky seksion është i rezervuar për shënimet tuaja personale specifike për këtë CV. Kjo përmbajtje mbetet private dhe nuk shpërndahet me askënd tjetër."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:216
msgid "Tip: You can name the resume referring to the position you are applying for."
msgstr "Këshillë: Ju mund të emërtoni CV-në duke iu referuar pozicionit për të cilin po aplikoni."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:39
msgctxt "Name of the Award"
msgid "Title"
msgstr "Titulli"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:196
msgid "Title"
msgstr "Titulli"

#: apps/client/src/pages/builder/_components/toolbar.tsx:138
msgid "Toggle Page Break Line"
msgstr ""

#: apps/client/src/pages/builder/_components/toolbar.tsx:150
msgid "Toggle Page Numbers"
msgstr "Aktivizo/çaktivizo numrat e faqeve"

#: apps/client/src/pages/home/<USER>/features/index.tsx:68
msgid "Track views and downloads"
msgstr "Gjurmo shikimet dhe shkarkimet"

#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/auth/verify-otp/page.tsx:57
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:129
msgid "Two-Factor Authentication"
msgstr "Vërtetimi me dy faktorë"

#: apps/client/src/services/errors/translate-error.ts:31
msgid "Two-factor authentication is already enabled for this account."
msgstr "Vërtetimi me dy faktorë veçse është aktivizuar për këtë llogari."

#: apps/client/src/services/errors/translate-error.ts:28
msgid "Two-factor authentication is not enabled for this account."
msgstr "Vërtetimi me dy faktorë nuk është aktivizuar për këtë llogari."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:84
msgid "Type <0>delete</0> to confirm deleting your account."
msgstr "Shkruani <0>delete</0> për të konfirmuar fshirjen e llogarisë tuaj."

#. For example, Bachelor's Degree or Master's Degree
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:54
msgid "Type of Study"
msgstr "Lloji i studimit"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:79
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:76
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:77
msgid "Typography"
msgstr "Tipografia"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:203
msgid "Underline Links"
msgstr "Nënvizoni linket"

#: apps/client/src/pages/builder/_components/toolbar.tsx:76
msgid "Undo"
msgstr "Zhbëj"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:52
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:110
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:161
msgid "Unlock"
msgstr "Shkyç"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:44
msgid "Unlocking a resume will allow you to make changes to it again."
msgstr "Shkyçja e një CV-je ju lejon të bëni ndryshime në të përsëri."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Unverified"
msgstr "I paverifikuar"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:164
msgid "Update an existing item"
msgstr "Përditëso një vlerë ekzistuese"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:179
msgid "Update an existing resume"
msgstr "Përditësoni një CV ekzistuese"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:212
msgid "Upload a file from one of the accepted sources to parse existing data and import it into Reactive Resume for easier editing."
msgstr "Analizo të dhënat ekzistuese dhe importoj ato në Reactive Resume për modifikim më të lehtë duke ngarkuar një skedar nga një prej burimeve të pranuara."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:73
msgid "URL"
msgstr "Linku"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:61
msgid "URL must start with https://"
msgstr "Linku duhet të fillojë me https://"

#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/backup-otp/page.tsx:57
msgid "Use your backup code"
msgstr "Përdor kodin rezervë"

#: apps/client/src/services/errors/translate-error.ts:13
msgid "User does not have an associated 'secrets' record. Please report this issue on GitHub."
msgstr ""

#: apps/client/src/pages/auth/register/page.tsx:119
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:55
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:167
msgid "Username"
msgstr "Emri i përdoruesit"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:13
msgid "Users Signed Up"
msgstr "Përdorues të regjistruar"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:296
msgid "Validate"
msgstr "Valido"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:314
msgid "Validated"
msgstr "U validua"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:97
msgid "Value"
msgstr "Vlera"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Verified"
msgstr "I verifikuar"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:161
msgid "Verify that two-factor authentication has been setup correctly"
msgstr "Sigurohuni që vërtetimi me dy faktorë është konfiguruar saktë"

#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-email/page.tsx:48
msgid "Verify your email address"
msgstr "Verifikoni email adresën tuaj"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:26
msgid "Version 4"
msgstr "Versioni 4"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:51
msgid "Views"
msgstr "Shikime"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:87
msgid "Visible"
msgstr "E dukshme"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:70
msgid "Visit <0>Phosphor Icons</0> for a list of available icons"
msgstr ""

#: apps/client/src/pages/auth/verify-email/page.tsx:61
msgid "We verify your email address only to ensure that we can send you a password reset link in case you forget your password."
msgstr "Ne e verifikojmë adresën tuaj të emailit vetëm për t'u siguruar që mund t'ju dërgojmë linkun për rivendosje të fjalëkalimit në rast se harroni fjalëkalimin tuaj."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:124
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:100
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:95
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:69
msgid "Website"
msgstr "Faqja e internetit"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:32
msgid "What's new in the latest version"
msgstr "Çfarë ka të re në versionin e fundit"

#: apps/client/src/pages/public/error.tsx:26
msgid "You are not authorized to access this page."
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:68
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:114
msgid "You can add multiple keywords by separating them with a comma or pressing enter."
msgstr "Mund të shtoni fjalë kyçe duke i ndarë me presje ose duke shtypur enter."

#: apps/client/src/pages/auth/login/page.tsx:99
msgid "You can also enter your username."
msgstr "Ju gjithashtu mund të jepni emrin tuaj të përdoruesit."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:103
msgid "You can also integrate with Ollama simply by setting the API key to `sk-1234567890abcdef` and the Base URL to your Ollama URL, i.e. `http://localhost:11434/v1`. You can also pick and choose models and set the max tokens as per your preference."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:81
msgid "You can make use of the OpenAI API to help you generate content, or improve your writing while composing your resume."
msgstr "Ju mund të përdorni OpenAI API për t'ju ndihmuar të krijoni përmbajtje ose ta përmirësoni shkrimin tuaj gjatë hartimit të CV-së tuaj."

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:40
msgid "You can track the number of views your resume has received, or how many people have downloaded the resume by enabling public sharing."
msgstr "Ju mund të gjurmoni numrin e shikimeve dhe shkarkimeve që ka marrë CV-ja juaj duke e shpërndarë atë me publikun."

#: apps/client/src/pages/public/error.tsx:20
msgid "You don't have permission to access this page."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:87
msgid "You have the option to <0>obtain your own OpenAI API key</0>. This key empowers you to leverage the API as you see fit. Alternatively, if you wish to disable the AI features in Reactive Resume altogether, you can simply remove the key from your settings."
msgstr ""

#: apps/client/src/pages/auth/verify-email/page.tsx:50
msgid "You should have received an email from <0>Reactive Resume</0> with a link to verify your account."
msgstr "Duhet të keni pranuar një email nga <0>Reactive Resume</0> me një link për të verifikuar llogarinë tuaj."

#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/forgot-password/page.tsx:54
msgid "You've got mail!"
msgstr "Keni mesazhe të palexuara!"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:52
msgid "Your account and all your data has been deleted successfully. Goodbye!"
msgstr "Llogaria juaj dhe të gjitha të dhënat tuaja janë fshirë me sukses."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:191
msgid "Your API key is securely stored in the browser's local storage and is only utilized when making requests to OpenAI via their official SDK. Rest assured that your key is not transmitted to any external server except when interacting with OpenAI's services."
msgstr ""

#: apps/client/src/pages/auth/verify-email/page.tsx:28
msgid "Your email address has been verified successfully."
msgstr "Adresa juaj e emailit është verifikuar me sukses."

#: apps/client/src/services/openai/client.ts:11
msgid "Your OpenAI API Key has not been set yet. Please go to your account settings to enable OpenAI Integration."
msgstr "Çelësi juaj OpenAI API nuk është konfiguruar ende. Ju lutem shkoni te parametrat e llogarisë tuaj për të aktivizuar integrimin me OpenAI."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:56
msgid "Your password has been updated successfully."
msgstr "Fjalëkalimi juaj u përditësua."

#: apps/client/src/pages/builder/_components/toolbar.tsx:112
msgid "Zoom In"
msgstr "Zmadho"

#: apps/client/src/pages/builder/_components/toolbar.tsx:118
msgid "Zoom Out"
msgstr "Zvogëlo"

