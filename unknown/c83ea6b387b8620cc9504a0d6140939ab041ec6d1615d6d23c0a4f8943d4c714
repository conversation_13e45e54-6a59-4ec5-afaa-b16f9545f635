{"$schema": "./node_modules/nx/schemas/nx-schema.json", "nxCloudAccessToken": "MTQ1OWFiOGUtODVjZC00YzI2LTliZTgtZDBlNWJmODc4NzM0fHJlYWQ=", "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}, "test": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/lint-test-build.yml"]}, "generators": {"@nx/react": {"application": {"style": "css", "linter": "eslint", "bundler": "vite", "babel": true}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint", "unitTestRunner": "vitest"}}}}